
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebAPI", "src\WebAPI\WebAPI.csproj", "{ABE4FC60-C604-44C5-B186-082D025BC948}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{2A36E673-E36C-4A5E-8FD3-9B6D63BD4267}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SharedKernel", "src\SharedKernel\SharedKernel.csproj", "{CFF1AAA5-3BD1-441E-B1E2-EE4CFA9974AF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{E533574C-BD72-4180-BBE4-91261C1E9352}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Architecture.Tests", "tests\Architecture.Tests\Architecture.Tests.csproj", "{A44DE559-DBB4-4439-B917-4DB533752216}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebAPI.Functional.Tests", "tests\WebAPI.Functional.Tests\WebAPI.Functional.Tests.csproj", "{7CF444F3-B504-4CED-80F5-E358216B6DCE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebAPI.Integration.Tests", "tests\WebAPI.Integration.Tests\WebAPI.Integration.Tests.csproj", "{59A19026-9555-49E7-BA52-7E322322BCD5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebAPI.Unit.Tests", "tests\WebAPI.Unit.Tests\WebAPI.Unit.Tests.csproj", "{C12DB1AF-503E-4D54-9866-4FBA1DC1D954}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{ABE4FC60-C604-44C5-B186-082D025BC948}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ABE4FC60-C604-44C5-B186-082D025BC948}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ABE4FC60-C604-44C5-B186-082D025BC948}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ABE4FC60-C604-44C5-B186-082D025BC948}.Release|Any CPU.Build.0 = Release|Any CPU
		{CFF1AAA5-3BD1-441E-B1E2-EE4CFA9974AF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFF1AAA5-3BD1-441E-B1E2-EE4CFA9974AF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFF1AAA5-3BD1-441E-B1E2-EE4CFA9974AF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFF1AAA5-3BD1-441E-B1E2-EE4CFA9974AF}.Release|Any CPU.Build.0 = Release|Any CPU
		{A44DE559-DBB4-4439-B917-4DB533752216}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A44DE559-DBB4-4439-B917-4DB533752216}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A44DE559-DBB4-4439-B917-4DB533752216}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A44DE559-DBB4-4439-B917-4DB533752216}.Release|Any CPU.Build.0 = Release|Any CPU
		{7CF444F3-B504-4CED-80F5-E358216B6DCE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7CF444F3-B504-4CED-80F5-E358216B6DCE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7CF444F3-B504-4CED-80F5-E358216B6DCE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7CF444F3-B504-4CED-80F5-E358216B6DCE}.Release|Any CPU.Build.0 = Release|Any CPU
		{59A19026-9555-49E7-BA52-7E322322BCD5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59A19026-9555-49E7-BA52-7E322322BCD5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59A19026-9555-49E7-BA52-7E322322BCD5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59A19026-9555-49E7-BA52-7E322322BCD5}.Release|Any CPU.Build.0 = Release|Any CPU
		{C12DB1AF-503E-4D54-9866-4FBA1DC1D954}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C12DB1AF-503E-4D54-9866-4FBA1DC1D954}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C12DB1AF-503E-4D54-9866-4FBA1DC1D954}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C12DB1AF-503E-4D54-9866-4FBA1DC1D954}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{ABE4FC60-C604-44C5-B186-082D025BC948} = {2A36E673-E36C-4A5E-8FD3-9B6D63BD4267}
		{CFF1AAA5-3BD1-441E-B1E2-EE4CFA9974AF} = {2A36E673-E36C-4A5E-8FD3-9B6D63BD4267}
		{A44DE559-DBB4-4439-B917-4DB533752216} = {E533574C-BD72-4180-BBE4-91261C1E9352}
		{7CF444F3-B504-4CED-80F5-E358216B6DCE} = {E533574C-BD72-4180-BBE4-91261C1E9352}
		{59A19026-9555-49E7-BA52-7E322322BCD5} = {E533574C-BD72-4180-BBE4-91261C1E9352}
		{C12DB1AF-503E-4D54-9866-4FBA1DC1D954} = {E533574C-BD72-4180-BBE4-91261C1E9352}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4B25AF0B-5F99-4285-BB2D-FA93FFD9CF7C}
	EndGlobalSection
EndGlobal

