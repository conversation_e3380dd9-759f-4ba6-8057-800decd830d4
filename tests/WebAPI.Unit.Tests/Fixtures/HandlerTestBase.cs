using AutoFixture;
using WebAPI.Persistence;
using WebAPI.Unit.Tests.Fixtures;

namespace Apollo.Application.Unit.Tests.Common;
public class HandlerTestBase : IDisposable
{
    protected readonly ApplicationDbContext _dbContext;
    protected readonly Fixture _fixture;
    protected readonly CancellationToken _cancellationToken = CancellationToken.None;
    public HandlerTestBase()
    {
        _dbContext = ApplicationDbContextFactory.Create();
        _fixture = new Fixture();
    }
    public void Dispose()
    {
        ApplicationDbContextFactory.Destroy(_dbContext);
    }
}
