using Microsoft.EntityFrameworkCore;
using Moq;
using SharedKernel.Services;
using WebAPI.Persistence;

namespace WebAPI.Unit.Tests.Fixtures;
public class ApplicationDbContextFactory
{
    public static ApplicationDbContext Create()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
               .UseInMemoryDatabase(Guid.NewGuid().ToString())
               .Options;

        var dateTimeService = new Mock<IDateTimeService>();
        var currentUserService = new Mock<ICurrentUserService>();

        currentUserService.Setup(x => x.IdentityId).Returns("00000000-0000-0000-0000-000000000000");

        var dbContext = new ApplicationDbContext(options, dateTimeService.Object, currentUserService.Object);
        dbContext.Database.EnsureCreated();
        dbContext.SaveChanges();
        return dbContext;
    }
    public static void Destroy(ApplicationDbContext context)
    {
        context.Database.EnsureDeleted();
        context.Dispose();
    }
}
