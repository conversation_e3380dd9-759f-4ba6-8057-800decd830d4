using System.Text.Json;
using SharedKernel.Services.ColibriApi.UniqueAiMetadata;

namespace WebAPI.Unit.Tests.Features.Metadata;

public class UniqueAiMetadataConverterTests
{
    private readonly UniqueAiMetadataConverter _service;

    public UniqueAiMetadataConverterTests()
    {
        _service = new UniqueAiMetadataConverter();
    }

    [Fact]
    public void ConvertToUniqueAiMetadataResponse_ShouldReturnCorrectMetadata_WhenValidResponse()
    {
        // Arrange
        const string jsonResponse =
            "{\"organisations\":[\"org1\",\"org2\",\"org3\",\"org4\"],\"people\":[\"Person1\",\"Person2\"],\"programmes\":[\"Prog1\",\"Prog2\"]}";
        var response = JsonSerializer.Deserialize<UniqueAiMetadataResponse>(jsonResponse);

        // Act
        var result = _service.ConvertToUniqueAiMetadataResponse(response);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(new List<string> { "Person1", "Person2" }, result.People);
        Assert.Equal(new List<string> { "org1", "org2", "org3", "org4" }, result.Organisations);
        Assert.Equal(new List<string> { "Prog1", "Prog2" }, result.Programmes);
    }

    [Fact]
    public void ConvertToUniqueAiMetadataResponse_ShouldReturnEmptyLists_WhenNullResponse()
    {
        // Arrange
        UniqueAiMetadataResponse? response = null;

        // Act
        var result = _service.ConvertToUniqueAiMetadataResponse(response);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.People);
        Assert.Empty(result.Organisations);
        Assert.Empty(result.Programmes);
    }

    [Fact]
    public void ConvertToUniqueAiMetadataResponse_ShouldReturnListsExcludingNonStringValues_WhenMixedTypes()
    {
        // Arrange
        const string jsonResponse =
            "{\"organisations\":[\"org1\", 123, \"org3\"],\"people\":[\"Person1\", null, \"Person3\"],\"programmes\":[\"Prog1\", {}, \"Prog3\"]}";
        var response = JsonSerializer.Deserialize<UniqueAiMetadataResponse>(jsonResponse);

        // Act
        var result = _service.ConvertToUniqueAiMetadataResponse(response);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(new List<string> { "Person1", "Person3" }, result.People);
        Assert.Equal(new List<string> { "org1", "org3" }, result.Organisations);
        Assert.Equal(new List<string> { "Prog1", "Prog3" }, result.Programmes);
    }
}
