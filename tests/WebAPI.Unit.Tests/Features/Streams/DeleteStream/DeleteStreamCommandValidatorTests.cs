using AutoFixture;
using FluentValidation.TestHelper;
using SharedKernel.Entities;
using WebAPI.Features.Streams.DeleteStream;

namespace WebAPI.Unit.Tests.Features.Streams.DeleteStream;
public class DeleteStreamCommandValidatorTests
{
    private readonly DeleteStreamCommandValidator _validator;
    private readonly Fixture _fixture;
    public DeleteStreamCommandValidatorTests()
    {
        _validator = new DeleteStreamCommandValidator();
        _fixture = new Fixture();
    }
    [Fact]
    public void DeleteStreamCommandValidator_WhenStreamIdIsEmpty_ReturnsFailure()
    {
        // Arrange
        var command = _fixture.Build<DeleteStreamCommand>()
                              .With(c => c.Id, new StreamId(Guid.Empty))
                              .Create();
        // Act
        var result = _validator.TestValidate(command);
        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(c => c.Id);
    }

    [Fact]
    public void DeleteStreamCommandValidator_WhenStreamIdIsNotEmpty_ReturnsSuccess()
    {
        // Arrange
        var command = _fixture.Create<DeleteStreamCommand>();
        // Act
        var result = _validator.TestValidate(command);
        // Assert
        result.IsValid.Should().BeTrue();
    }
}
