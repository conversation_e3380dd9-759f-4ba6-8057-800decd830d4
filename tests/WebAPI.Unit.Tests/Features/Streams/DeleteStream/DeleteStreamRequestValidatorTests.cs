using AutoFixture;
using FluentValidation.TestHelper;
using WebAPI.Features.Streams.DeleteStream;

namespace WebAPI.Unit.Tests.Features.Streams.DeleteStream;
public class DeleteStreamRequestValidatorTests
{
    private readonly DeleteStreamRequestValidator _validator;
    private readonly Fixture _fixture;
    public DeleteStreamRequestValidatorTests()
    {
        _validator = new DeleteStreamRequestValidator();
        _fixture = new Fixture();
    }

    [Fact]
    public void DeleteStreamRequestValidator_WhenStreamIdIsEmpty_ReturnsFailure()
    {
        // Arrange
        var request = _fixture.Build<DeleteStreamRequest>()
                              .With(r => r.Id, Guid.Empty)
                              .Create();
        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(r => r.Id);
    }

    [Fact]
    public void DeleteStreamRequestValidator_WhenStreamIdIsNotEmpty_ReturnsSuccess()
    {
        // Arrange
        var request = _fixture.Create<DeleteStreamRequest>();

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }
}
