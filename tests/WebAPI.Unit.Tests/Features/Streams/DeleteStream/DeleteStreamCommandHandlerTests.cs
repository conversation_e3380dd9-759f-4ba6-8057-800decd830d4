using Apollo.Application.Unit.Tests.Common;
using AutoFixture;
using FluentResults;
using Moq;
using SharedKernel.Entities;
using SharedKernel.Services;
using SharedKernel.Services.ColibriApi.AddAmplifiers;
using WebAPI.Features.StreamAlerts.HangfireApi;
using WebAPI.Features.Streams.DeleteStream;
using Stream = SharedKernel.Entities.Stream;

namespace WebAPI.Unit.Tests.Features.Streams.DeleteStream;
public class DeleteStreamCommandHandlerTests : HandlerTestBase
{
    private readonly DeleteStreamCommandHandler _handler;

    public DeleteStreamCommandHandlerTests()
    {
        Mock<IHangfireApi> hangfireApi = new Mock<IHangfireApi>();
        hangfireApi.Setup(api => api.DeleteScheduleStreamAlertJob(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());
        _handler = new DeleteStreamCommandHandler(_dbContext, hangfireApi.Object);
    }

    private void SeedRelatedEntities(StreamId streamId)
    {

        var amplifiers = _fixture.CreateMany<string>(3)
            .Select(name => Amplifier.Create(streamId, name, 0.5))
            .ToList();

        var fewShots = _fixture.CreateMany<long>(2)
            .Select(itemId => FewShot.Create(streamId, itemId, 0.8))
            .ToList();

        var streamMetadataFilters = _fixture.CreateMany<string>(4)
            .Select(filterValue => StreamMetadataFilter.Create(streamId, StreamMetadataFilterName.People, true, filterValue))
            .ToList();

        var streamFilterTypeCodes = _fixture.CreateMany<int>(2)
            .Select(typeCode => StreamFilterTypeCode.Create(typeCode, streamId, true))
        .ToList();

        _dbContext.Amplifier.AddRange(amplifiers);
        _dbContext.FewShot.AddRange(fewShots);
        _dbContext.StreamMetadataFilter.AddRange(streamMetadataFilters);
        _dbContext.StreamFilterTypeCode.AddRange(streamFilterTypeCodes);
    }


    [Fact]
    public async Task Handle_WhenStreamExists_DeletesStreamAndRelatedEntities()
    {
        // Arrange
        var stream = _fixture.Create<Stream>();
        _dbContext.Streams.Add(stream);
        SeedRelatedEntities(stream.Id);
        await _dbContext.SaveChangesAsync();

        var command = new DeleteStreamCommand(stream.Id);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);


        // Assert
        result.IsSuccess.Should().BeTrue();
        _dbContext.Streams.Should().NotContain(stream);
        _dbContext.Amplifier.Should().NotContain(a => a.StreamId == stream.Id);
        _dbContext.FewShot.Should().NotContain(fs => fs.StreamId == stream.Id);
        _dbContext.StreamMetadataFilter.Should().NotContain(sf => sf.StreamId == stream.Id);
        _dbContext.StreamFilterTypeCode.Should().NotContain(sfc => sfc.StreamId == stream.Id);
    }

    [Fact]
    public async Task Handle_WhenStreamDoesNotExist_ReturnsFailure()
    {
        // Arrange
        var command = new DeleteStreamCommand(new StreamId(Guid.NewGuid()));

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsFailed.Should().BeTrue();
        result.Errors.Should()
            .ContainSingle(e => e.Message.Contains($"Stream with ID {command.Id} not found."));
    }
}
