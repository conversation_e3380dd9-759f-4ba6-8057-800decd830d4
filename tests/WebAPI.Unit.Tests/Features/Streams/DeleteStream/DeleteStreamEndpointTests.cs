using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using SharedKernel.Entities;
using WebAPI.Features.Streams.DeleteStream;

namespace WebAPI.Unit.Tests.Features.Streams.DeleteStream;

public class DeleteStreamEndpointTests
{
    private readonly Mock<ISender> _mockSender;
    private readonly DeleteStreamEndpoint _endpoint;

    public DeleteStreamEndpointTests()
    {
        _mockSender = new Mock<ISender>();
        _endpoint = new DeleteStreamEndpoint(_mockSender.Object);
    }

    [Fact]
    public async Task HandleAsync_WhenStreamExists_ShouldSendDeleteStreamCommandAndReturnNoContent()
    {
        // Arrange
        var streamId = Guid.NewGuid();
        var command = new DeleteStreamCommand(new StreamId(streamId));

        _mockSender.Setup(sender => sender.Send(It.IsAny<DeleteStreamCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());

        // Act
        var result = await _endpoint.HandleAsync(streamId, CancellationToken.None);

        // Assert
        _mockSender.Verify(sender => sender.Send(It.Is<DeleteStreamCommand>
            (c => c.Id.Value == streamId), CancellationToken.None),
            Times.Once);

        result.Should().BeOfType<ActionResult<Response>>();
        result.Result.Should().BeOfType<NoContentResult>();
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnNotFound_WhenStreamDoesNotExist()
    {
        // Arrange
        var streamId = Guid.NewGuid();
        var command = new DeleteStreamCommand(new StreamId(streamId));
        var resultFail = Result.Fail("The stream with id {streamId} was not found");

        _mockSender
            .Setup(s => s.Send(It.Is<DeleteStreamCommand>(c => c.Id.Value == streamId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(resultFail);

        // Act
        var result = await _endpoint.HandleAsync(streamId);

        // Assert
        _mockSender.Verify(sender => sender.Send(It.Is<DeleteStreamCommand>(c =>
                        c.Id.Value == streamId),
                        CancellationToken.None), Times.Once);

        result.Should().BeOfType<ActionResult<Response>>();
        result.Result.Should().BeOfType<NotFoundObjectResult>();
    }
}
