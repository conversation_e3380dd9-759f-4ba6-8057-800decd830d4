using Apollo.Application.Unit.Tests.Common;
using AutoFixture;
using Microsoft.Extensions.Logging;
using Moq;
using SharedKernel.Entities;
using SharedKernel.Services;
using WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;

namespace WebAPI.Unit.Tests.Features.Streams.UpdateStreamAlertLastSent;

public class UpdateStreamAlertLastSentCommandHandlerTests : HandlerTestBase
{
    private readonly UpdateStreamAlertLastSentCommandHandler _handler;

    public UpdateStreamAlertLastSentCommandHandlerTests()
    {
        Mock<ILogger<UpdateStreamAlertLastSentCommandHandler>> loggerMock =
            new Mock<ILogger<UpdateStreamAlertLastSentCommandHandler>>();
        Mock<IDateTimeService> dateTimeServiceMock = new Mock<IDateTimeService>();

        _handler = new UpdateStreamAlertLastSentCommandHandler(_dbContext, new AlertCalculator(dateTimeServiceMock.Object), loggerMock.Object);
    }


    [Fact]
    public async Task Handle_ShouldUpdateAlertLastSent_WhenValidCommand()
    {
        // Arrange
        var streamAlert = _fixture.Create<StreamAlert>();
        _dbContext.StreamAlerts.Add(streamAlert);
        await _dbContext.SaveChangesAsync();

        var command = new UpdateStreamAlertLastSentCommand(streamAlert.Id.Value, DateTimeOffset.UtcNow);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var updatedStream = await _dbContext.StreamAlerts.FindAsync(streamAlert.Id);
        updatedStream?.LastSent.Should().Be(command.LastSent);
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenStreamNotFound()
    {
        // Arrange
        var id = Guid.NewGuid();
        var command = new UpdateStreamAlertLastSentCommand(id, DateTimeOffset.UtcNow);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().ContainSingle(x => x.Message == $"Stream alert with ID {id} was not found");
    }
}
