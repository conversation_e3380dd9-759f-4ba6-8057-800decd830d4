using FluentValidation.TestHelper;
using WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;

namespace WebAPI.Unit.Tests.Features.Streams.UpdateStreamAlertLastSent;

public class UpdateStreamAlertLastSentRequestValidatorTests
{
    private readonly UpdateStreamAlertLastSentRequestValidator _validator = new();
    private readonly Guid _id = Guid.NewGuid();

    [Fact]
    public void Should_Have_Error_When_StreamAlertId_Is_Empty()
    {
        var request = new UpdateStreamAlertLastSentRequest(Guid.Empty, DateTimeOffset.UtcNow, "<EMAIL>", 8);
        var result = _validator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.StreamAlertId);
    }

    [Fact]
    public void Should_Not_Have_Error_When_StreamAlertId_Is_Provided()
    {
        var request = new UpdateStreamAlertLastSentRequest(_id, DateTimeOffset.UtcNow, "<EMAIL>", 8);
        var result = _validator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.StreamAlertId);
    }

    [Fact]
    public void Should_Have_Error_When_LastSent_Is_In_The_Future()
    {
        var request = new UpdateStreamAlertLastSentRequest(_id, DateTimeOffset.UtcNow.AddMinutes(1), "<EMAIL>", 8);
        var result = _validator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.LastSent)
            .WithErrorMessage("LastSent cannot be in the future.");
    }

    [Fact]
    public void Should_Not_Have_Error_When_LastSent_Is_Null()
    {
        var request = new UpdateStreamAlertLastSentRequest(_id, null, "<EMAIL>", 8);
        var result = _validator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.LastSent);
    }

    [Fact]
    public void Should_Not_Have_Error_When_LastSent_Is_In_The_Past_Or_Present()
    {
        var request = new UpdateStreamAlertLastSentRequest(_id, DateTimeOffset.UtcNow.AddHours(-1), "<EMAIL>", 8);
        var result = _validator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.LastSent);
    }
}
