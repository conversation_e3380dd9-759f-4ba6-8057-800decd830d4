using AutoFixture;
using AutoMapper;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using WebAPI.Features.StreamAlerts.UpdateStreamAlert;
using WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;

namespace WebAPI.Unit.Tests.Features.Streams.UpdateStreamAlertLastSent;

public class UpdateStreamAlertLastSentEndpointTests
{
    private readonly Mock<ISender> _senderMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly UpdateStreamAlertLastSentEndpoint _endpoint;
    private readonly Fixture _fixture;

    public UpdateStreamAlertLastSentEndpointTests()
    {
        _senderMock = new Mock<ISender>();
        _mapperMock = new Mock<IMapper>();
        _endpoint = new UpdateStreamAlertLastSentEndpoint(_senderMock.Object, _mapperMock.Object);
        _fixture = new Fixture();
    }

    [Fact]
    public async Task HandleAsync_WhenCommandSucceeds_ReturnsCreatedResult()
    {
        // Arrange
        var request = _fixture.Create<UpdateStreamAlertLastSentRequest>();
        _senderMock.Setup(s => s.Send(It.IsAny<UpdateStreamAlertLastSentCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());

        // Act
        var result = await _endpoint.HandleAsync(request);

        // Assert
        _senderMock.Verify(sender => sender.Send(It.Is<UpdateStreamAlertLastSentCommand>(c =>
                c.StreamAlertId.Value == request.StreamAlertId &&
                c.LastSent == request.LastSent),
            CancellationToken.None), Times.Once);


        result.Should().BeOfType<ActionResult<UpdateStreamAlertResponse>>();
        result.Result.Should().BeOfType<OkObjectResult>();
    }

    [Fact]
    public async Task HandleAsync_WhenCommandFails_ReturnsBadRequestWithProblemDetails()
    {
        // Arrange
        var request = _fixture.Create<UpdateStreamAlertLastSentRequest>();
        var failureResult = Result.Fail("Error occurred");
        _senderMock.Setup(s => s.Send(It.IsAny<UpdateStreamAlertLastSentCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(failureResult);

        // Act
        var result = await _endpoint.HandleAsync(request);

        // Assert
        _senderMock.Verify(sender => sender.Send(It.Is<UpdateStreamAlertLastSentCommand>(c =>
                c.StreamAlertId.Value == request.StreamAlertId &&
                c.LastSent == request.LastSent),
            CancellationToken.None), Times.Once);

        result.Should().BeOfType<ActionResult<UpdateStreamAlertResponse>>();
        result.Result.Should().BeOfType<BadRequestObjectResult>();
    }
}
