using Moq;
using SharedKernel.Entities;
using SharedKernel.Services;
using WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;

namespace WebAPI.Unit.Tests.Features.Streams.UpdateStreamAlertLastSent;

public class AlertCalculatorTests
{
    private readonly AlertCalculator _alertCalculator;

    public AlertCalculatorTests()
    {
        Mock<IDateTimeService> dateTimeServiceMock = new Mock<IDateTimeService>();
        dateTimeServiceMock.Setup(x => x.UtcNow)
            .Returns(new DateTimeOffset(2025, 1, 1, 14, 0, 0, TimeSpan.Zero));

        _alertCalculator = new AlertCalculator(dateTimeServiceMock.Object);
    }

    [Fact]
    public void CalculateNextAlertDate_WhenCurrentDateTimeIsBeforeStartDateTime_ShouldReturnStartDateTime()
    {
        // Arrange
        var startDateTime = new DateTimeOffset(2025, 1, 6, 7, 0, 0, TimeSpan.Zero);
        var endDateTime = new DateTimeOffset(2025, 1, 30, 10, 0, 0, TimeSpan.Zero);
        const StreamAlertFrequency frequency = StreamAlertFrequency.Hour;

        // Act
        var result = _alertCalculator.CalculateNextAlertDate(startDateTime, endDateTime, frequency);

        // Assert
        result.Should().Be(startDateTime);
    }

    [Fact]
    public void CalculateNextAlertDate_WhenEndDateTimeIsSetAndCurrentDateTimeIsPastEndDateTime_ShouldReturnNull()
    {
        // Arrange
        var startDateTime = new DateTimeOffset(2025, 1, 1, 7, 0, 0, TimeSpan.Zero);
        var endDateTime = new DateTimeOffset(2025, 1, 1, 10, 0, 0, TimeSpan.Zero);
        const StreamAlertFrequency frequency = StreamAlertFrequency.Hour;

        // Act
        var result = _alertCalculator.CalculateNextAlertDate(startDateTime, endDateTime, frequency);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void CalculateNextAlertDate_WhenFrequencyIsNone_ShouldReturnNull()
    {
        // Arrange
        var startDateTime = new DateTimeOffset(2025, 1, 1, 7, 0, 0, TimeSpan.Zero);
        var endDateTime = new DateTimeOffset(2025, 1, 2, 10, 0, 0, TimeSpan.Zero);
        const StreamAlertFrequency frequency = StreamAlertFrequency.None;

        // Act
        var result = _alertCalculator.CalculateNextAlertDate(startDateTime, endDateTime, frequency);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void CalculateNextAlertDate_WhenFrequencyIsDaily_ShouldReturnNextDailyAlertDate()
    {
        // Arrange
        var startDateTime = new DateTimeOffset(2025, 1, 1, 7, 0, 0, TimeSpan.Zero);
        var endDateTime = new DateTimeOffset(2025, 1, 30, 14, 0, 0, TimeSpan.Zero);
        const StreamAlertFrequency frequency = StreamAlertFrequency.Day;

        // Act
        var result = _alertCalculator.CalculateNextAlertDate(startDateTime, endDateTime, frequency);

        // Assert
        result.Should().Be(new DateTimeOffset(2025, 1, 2, 7, 0, 0, TimeSpan.Zero));
    }

    [Fact]
    public void CalculateNextAlertDate_WhenFrequencyIsWeekly_ShouldReturnNextWeeklyAlertDate()
    {
        // Arrange
        var startDateTime = new DateTimeOffset(2025, 1, 1, 7, 0, 0, TimeSpan.Zero);
        var endDateTime = new DateTimeOffset(2025, 1, 30, 14, 0, 0, TimeSpan.Zero);
        const StreamAlertFrequency frequency = StreamAlertFrequency.Week;

        // Act
        var result = _alertCalculator.CalculateNextAlertDate(startDateTime, endDateTime, frequency);

        // Assert
        result.Should().Be(new DateTimeOffset(2025, 1, 8, 7, 0, 0, TimeSpan.Zero));
    }

    [Fact]
    public void
        CalculateNextAlertDate_WhenFrequencyIsWeeklyAndCurrentDateTimeIsBeforeStartDateTime_ShouldReturnStartDateTime()
    {
        // Arrange
        var startDateTime = new DateTimeOffset(2025, 1, 14, 7, 0, 0, TimeSpan.Zero);
        var endDateTime = new DateTimeOffset(2025, 1, 30, 14, 0, 0, TimeSpan.Zero);
        const StreamAlertFrequency frequency = StreamAlertFrequency.Week;

        // Act
        var result = _alertCalculator.CalculateNextAlertDate(startDateTime, endDateTime, frequency);

        // Assert
        result.Should().Be(startDateTime);
    }

    [Fact]
    public void CalculateNextAlertDate_WhenFrequencyIsWeeklyAndCurrentDateTimeIsAfterEndDateTime_ShouldReturnNull()
    {
        // Arrange
        var startDateTime = new DateTimeOffset(2025, 1, 1, 6, 0, 0, TimeSpan.Zero);
        var endDateTime = new DateTimeOffset(2025, 1, 1, 10, 0, 0, TimeSpan.Zero);
        const StreamAlertFrequency frequency = StreamAlertFrequency.Week;

        // Act
        var result = _alertCalculator.CalculateNextAlertDate(startDateTime, endDateTime, frequency);

        // Assert
        result.Should().BeNull();
    }
}
