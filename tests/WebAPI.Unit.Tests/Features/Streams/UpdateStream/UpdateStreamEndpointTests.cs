using System.Net;
using AutoFixture;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using WebAPI.Features.Streams.GetIsMentionStream;
using WebAPI.Features.Streams.UpdateStream;
using Response = WebAPI.Features.Streams.Shared.Response;

namespace WebAPI.Unit.Tests.Features.Streams.UpdateStream;

public class UpdateStreamEndpointTests
{
    private readonly Mock<ISender> _senderMock;
    private readonly UpdateStreamEndpoint _endpoint;
    private readonly Fixture _fixture;

    public UpdateStreamEndpointTests()
    {
        _senderMock = new Mock<ISender>();
        _endpoint = new UpdateStreamEndpoint(_senderMock.Object);
        _fixture = new Fixture();
    }

    [Fact]
    public async Task HandleAsync_WhenCommandSucceeds_ReturnsCreatedResult()
    {
        // Arrange
        var isMention = new WebAPI.Features.Streams.GetIsMentionStream.Response(false);
        _senderMock.Setup(s => s.Send(It.IsAny<GetIsMentionStreamQuery>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(Result.Ok(isMention));

        var request = _fixture.Create<UpdateStreamRequest>();
        _senderMock.Setup(s => s.Send(It.IsAny<UpdateStreamCommand>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(Result.Ok());

        // Act
        var result = await _endpoint.HandleAsync(request);

        // Assert
        _senderMock.Verify(sender => sender.Send(It.Is<UpdateStreamCommand>(c =>
                        c.Id.Value == request.Id &&
                        c.Request.CompanyId == request.Request.CompanyId &&
                        c.Request.Prompt == request.Request.Prompt &&
                        c.Request.AiGeneratedPrompt == request.Request.AiGeneratedPrompt &&
                        c.Request.Filters == request.Request.Filters &&
                        c.Request.ExclusionFilters == request.Request.ExclusionFilters &&
                        c.Request.AiParameters == request.Request.AiParameters &&
                        c.Request.AdvancedSettings == request.Request.AdvancedSettings),
                        CancellationToken.None), Times.Once);


        result.Should().BeOfType<ActionResult<Response>>();
        result.Result.Should().BeOfType<OkObjectResult>();
    }

    [Fact]
    public async Task HandleAsync_WhenCommandFails_ReturnsBadRequestWithProblemDetails()
    {
        // Arrange
        var isMention = new WebAPI.Features.Streams.GetIsMentionStream.Response(false);
        _senderMock.Setup(s => s.Send(It.IsAny<GetIsMentionStreamQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok(isMention));

        var request = _fixture.Create<UpdateStreamRequest>();
        var failureResult = Result.Fail("Error occurred");
        _senderMock.Setup(s => s.Send(It.IsAny<UpdateStreamCommand>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(failureResult);

        // Act
        var result = await _endpoint.HandleAsync(request);

        // Assert
        _senderMock.Verify(sender => sender.Send(It.Is<UpdateStreamCommand>(c =>
                        c.Id.Value == request.Id &&
                        c.Request.CompanyId == request.Request.CompanyId &&
                        c.Request.Prompt == request.Request.Prompt &&
                        c.Request.AiGeneratedPrompt == request.Request.AiGeneratedPrompt &&
                        c.Request.Filters == request.Request.Filters &&
                        c.Request.ExclusionFilters == request.Request.ExclusionFilters &&
                        c.Request.AiParameters == request.Request.AiParameters &&
                        c.Request.AdvancedSettings == request.Request.AdvancedSettings),
                        CancellationToken.None), Times.Once);

        result.Should().BeOfType<ActionResult<Response>>();
        result.Result.Should().BeOfType<BadRequestObjectResult>();
    }
}
