using AutoFixture;
using FluentValidation.TestHelper;
using SharedKernel.Entities;
using WebAPI.Features.Streams.Shared;
using WebAPI.Features.Streams.UpdateStream;

namespace WebAPI.Unit.Tests.Features.Streams.UpdateStream;

public class UpdateStreamCommandValidatorTests
{
    private readonly UpdateStreamCommandValidator _validator = new();
    private readonly Fixture _fixture = new();
    private readonly StreamId _streamId = new(Guid.NewGuid());

    [Fact]
    public void UpdateStreamCommandValidator_WhenValidRequest_ShouldNotHaveValidationError()
    {
        // Arrange
        var request = _fixture.Create<UpdateRequestResponse>();
        var command = new UpdateStreamCommand(_streamId, request, false);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenStreamIdIsEmpty_ShouldHaveValidationError()
    {
        // Arrange
        var request = _fixture.Create<UpdateRequestResponse>();
        var stream = new StreamId(Guid.Empty);
        var command = new UpdateStreamCommand(stream, request, false);

        // Act
        var result = _validator.TestValidate(command);
        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Id)
            .WithErrorMessage("'Id' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenNameIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Name, null as string)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Name)
            .WithErrorMessage("'Request Name' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenNameIsEmpty_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Name, string.Empty)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Name)
            .WithErrorMessage("'Request Name' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenPromptIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Prompt, null as string)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Prompt)
            .WithErrorMessage("'Request Prompt' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenPromptIsEmpty_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Prompt, string.Empty)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Prompt)
            .WithErrorMessage("'Request Prompt' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenFiltersIsNullAndIsMention_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Filters, null as Filters)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, true);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Filters)
            .WithErrorMessage("'Request Filters' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenFiltersIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Filters, null as Filters)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldNotHaveValidationErrorFor(x => x.Request.Filters);
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenFiltersPropertiesIsEmpty_ShouldNotHaveAnyValidationError()
    {
        var emptyFilters = new Filters([], [], [], [], []);
        var request = _fixture.Build<UpdateRequestResponse>().With(x => x.Filters, emptyFilters).Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenFiltersTypeCodeIdsIsNull_ShouldHaveValidationError()
    {
        var filters = new Filters([], [], [], [], null);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Filters, filters)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Filters.TypeCodeIds)
            .WithErrorMessage("'Filters Type Code Ids' must not be null.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenExclusionFiltersIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.ExclusionFilters, null as ExclusionFilters)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters)
            .WithErrorMessage("'Request Exclusion Filters' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenExclusionFiltersPropertiesIsEmpty_ShouldNotHaveAnyValidationError()
    {
        // Arrange
        var emptyExclusionFilters = new ExclusionFilters([], [], [], [], []);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.ExclusionFilters, emptyExclusionFilters)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenExclusionFiltersPeopleIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters(null, [], [], [], []);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.People)
            .WithErrorMessage("'Exclusion Filters People' must not be null.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenExclusionFiltersOrganisationIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters([], null, [], [], []);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.Organisations)
            .WithErrorMessage("'Exclusion Filters Organisation' must not be null.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenExclusionFiltersProgrammeIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters([], [], null, [], []);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.Programmes)
            .WithErrorMessage("'Exclusion Filters Programme' must not be null.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenExclusionFiltersThemeIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters([], [], [], null, []);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.Themes)
            .WithErrorMessage("'Exclusion Filters Theme' must not be null.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenExclusionFiltersTypeCodeIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters([], [], [], [], null);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.TypeCodeIds)
            .WithErrorMessage("'Exclusion Filters Type Code Ids' must not be null.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAiGeneratedPromptIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AiGeneratedPrompt, null as string)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.AiGeneratedPrompt)
            .WithErrorMessage("'Request Ai Generated Prompt' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAiGeneratedPromptIsEmpty_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AiGeneratedPrompt, string.Empty)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.AiGeneratedPrompt)
            .WithErrorMessage("'Request Ai Generated Prompt' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAiParametersIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AiParameters, null as AiParameters)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AiParameters)
            .WithErrorMessage("'Request Ai Parameters' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAiParametersAiModelIdsIsEmpty_ShouldHaveValidationError()
    {
        // Arrange
        var aiParameters = new AiParameters([], 1);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AiParameters, aiParameters)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AiParameters.AiModelIds)
            .WithErrorMessage("'Request Ai Parameters Ai Model Ids' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAiParametersAiModelIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var aiParameters = new AiParameters(null, 1);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AiParameters, aiParameters)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AiParameters.AiModelIds)
            .WithErrorMessage("'Request Ai Parameters Ai Model Ids' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAiParametersConfidenceIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var aiParameters = new AiParameters([Guid.NewGuid()], default);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AiParameters, aiParameters)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AiParameters.Confidence)
            .WithErrorMessage("'Request Ai Parameters Confidence' must not be empty.");
    }


    [Fact]
    public void UpdateStreamCommandValidator_WhenAdvancedSettingsIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, null as AdvancedSettings)
            .Create();

        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("'Request Advanced Settings' must not be empty.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAdvancedSettingsAmplifiersIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings(null, 10, [], 5);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings.Amplifiers)
            .WithErrorMessage("'Advanced Settings Amplifiers' must not be null.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAdvancedSettingsAmplifierRelevancyIsNull_ShouldNotHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], null, [], 5);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();
        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);
        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings.AmplifierRelevancy)
            .WithErrorMessage("'Advanced Settings Amplifier Relevancy' must not be null.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAdvancedSettingsFewShotItemIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 10, null, 5);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();
        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);
        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings.FewShotItemIds)
            .WithErrorMessage("'Few Shot Item Ids' must not be null.");
    }

    [Fact]
    public void UpdateStreamCommandValidator_WhenAdvancedSettingsFewShotRelevancyIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 10, [], null);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();
        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);
        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings.FewShotRelevancy)
            .WithErrorMessage("'Few Shot Relevancy' must not be null.");
    }

    [Fact]
    public void
        UpdateStreamCommandValidator_WhenAmplifiersHasValueAndAmplifierRelevancyIsZero_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings(["Amplifiers"], 0, [1], 10);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("If Amplifiers has value, Amplifier Relevancy must be greater than zero.");
    }

    [Fact]
    public void
        UpdateStreamCommandValidator_WhenAmplifiersRelevencyIsNotZeroAndAmplifiersIsEmpty_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 10, [1], 10);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("If Amplifiers has value, Amplifier Relevancy must be greater than zero.");
    }

    [Fact]
    public void
        UpdateStreamCommandValidator_WhenAmplifiersIsEmptyAndAmplifierRelevancyIsZero_ShouldNotHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 0, [1], 10);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void
        UpdateStreamCommandValidator_WhenFewShotItemIdsHasValueAndFewShotRelevancyIsZero_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings(["Amplifier"], 10, [1, 2, 3], 0);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("If Few Shot Item Id's has value, Few Shot Relevancy must be greater than zero.");
    }

    [Fact]
    public void
        UpdateStreamCommandValidator_WhenFewShotRelevancyIsNotZeroAndFewShotItemIdsIsEmpty_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings(["Amplifier"], 10, [], 10);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("If Few Shot Item Id's has value, Few Shot Relevancy must be greater than zero.");
    }

    [Fact]
    public void
        UpdateStreamCommandValidator_WhenFewShotItemIdsIsEmptyAndFewShotRelevancyIsZero_ShouldNotHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 0, [], 0);
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var command = new UpdateStreamCommand(_streamId, request, false);

        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionIsTrue_ShouldHaveValidationErrorForAiGeneratedPrompt()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AiGeneratedPrompt, "Some prompt")
            .Create();

        var command = new UpdateStreamCommand(new StreamId(Guid.NewGuid()), request, true);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.AiGeneratedPrompt)
            .WithErrorMessage("Mention Stream should not have an AI Generated Prompt.");
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionIsTrue_ShouldNotHaveValidationErrorForAiParameters()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AiParameters, null as AiParameters)
            .Create();

        var command = new UpdateStreamCommand(new StreamId(Guid.NewGuid()), request, true);

        var result = _validator.TestValidate(command);

        result.ShouldNotHaveValidationErrorFor(x => x.Request.AiParameters);
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionIsTrue_ShouldHaveValidationErrorForAdvancedSettings()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings,
                new AdvancedSettings(new List<string> { "Amplifier" }, 1, new List<long?> { 1 }, 1))
            .Create();

        var command = new UpdateStreamCommand(new StreamId(Guid.NewGuid()), request, true);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("Mention Stream should not have Advanced Settings.");
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionIsTrue_ShouldNotHaveValidationErrorForNullPrompt()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Prompt, null as string)
            .Create();

        var command = new UpdateStreamCommand(new StreamId(Guid.NewGuid()), request, true);

        var result = _validator.TestValidate(command);

        result.ShouldNotHaveValidationErrorFor(x => x.Request.Prompt);
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionIsTrue_ShouldHaveValidationErrorForPrompt()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Prompt, "Prompt")
            .Create();

        var command = new UpdateStreamCommand(new StreamId(Guid.NewGuid()), request, true);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Prompt);
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionAndFiltersHasNoPopulatedProperties_ShouldHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Filters, new Filters([], [], [], [], []))
            .Create();

        var command = new UpdateStreamCommand(new StreamId(Guid.NewGuid()), request, true);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Filters)
            .WithErrorMessage("At least one of People, Organisation, Programme or Theme must contain a filter.");
    }

    [Fact]
    public void
        CreateStreamCommandValidator_WhenIsMentionAndFiltersHasPopulatedProperties_ShouldNotHaveValidationError()
    {
        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Filters, new Filters([new Filter(Guid.NewGuid(), "Boris Johnson")], [], [], [], []))
            .Create();

        var command = new UpdateStreamCommand(new StreamId(Guid.NewGuid()), request, true);

        var result = _validator.TestValidate(command);

        result.ShouldNotHaveValidationErrorFor(x => x.Request.Filters);
    }
}
