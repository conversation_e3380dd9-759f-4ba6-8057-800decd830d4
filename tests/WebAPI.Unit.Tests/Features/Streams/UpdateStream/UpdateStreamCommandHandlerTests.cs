using Apollo.Application.Unit.Tests.Common;
using AutoFixture;
using FluentResults;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using SharedKernel.Entities;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.AddAmplifiers;
using SharedKernel.Services.ColibriApi.AddFewShots;
using SharedKernel.Services.UserApi;
using WebAPI.Features.Streams.Shared;
using WebAPI.Features.Streams.UpdateStream;
using static SharedKernel.Services.UserApi.UserApiClient;
using Stream = SharedKernel.Entities.Stream;

namespace WebAPI.Unit.Tests.Features.Streams.UpdateStream;

public class UpdateStreamCommandHandlerTests : HandlerTestBase
{
    private readonly Mock<IColibriApi> _colibriApiMock;
    private readonly Mock<ILogger<UpdateStreamCommandHandler>> _loggerMock;
    private readonly Mock<IUserApiClient> _userApiClientMock;
    private readonly UpdateStreamCommandHandler _handler;

    public UpdateStreamCommandHandlerTests()
    {
        _colibriApiMock = new Mock<IColibriApi>();
        _loggerMock = new Mock<ILogger<UpdateStreamCommandHandler>>();
        _userApiClientMock = new Mock<IUserApiClient>();

        _handler = new UpdateStreamCommandHandler(_dbContext, _colibriApiMock.Object, _userApiClientMock.Object,
            _loggerMock.Object);

        MockNecessaryMethods();
    }

    private void MockNecessaryMethods()
    {
        _colibriApiMock.Setup(api => api.AddAmplifiers(It.IsAny<AddAmplifiersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok(new AddAmplifiersResponse { ErrorAmplifiers = [] }));
        _colibriApiMock.Setup(api => api.AddFewShots(It.IsAny<AddFewShotsRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok(new AddFewShotsResponse { ErrorFewShots = [] }));
        _userApiClientMock.Setup(client => client.GetCompanyName(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok(new CompanyResponse("Test Company")));
    }

    private static void ValidateStream(UpdateStreamCommand command, Stream stream)
    {
        stream.Should().NotBeNull();
        stream.Should().BeEquivalentTo(command, options => options.ExcludingMissingMembers());
    }

    private void ValidateRelatedEntities(UpdateStreamCommand command, Stream updatedStream)
    {
        if (command.Request.Filters != null)
        {
            var expectedFilterCount = (command.Request.Filters.People?.Count() ?? 0) +
                                      (command.Request.Filters.Organisations?.Count() ?? 0) +
                                      (command.Request.Filters.Programmes?.Count() ?? 0) +
                                      (command.Request.Filters.Themes?.Count() ?? 0) +
                                      (command.Request.ExclusionFilters?.People?.Count() ?? 0) +
                                      (command.Request.ExclusionFilters?.Organisations?.Count() ?? 0) +
                                      (command.Request.ExclusionFilters?.Programmes?.Count() ?? 0) +
                                      (command.Request.ExclusionFilters?.Themes?.Count() ?? 0);
            _dbContext.StreamMetadataFilter.Count(f => f.StreamId == updatedStream.Id).Should().Be(expectedFilterCount);
        }

        if (command.Request.Filters?.TypeCodeIds != null)
        {
            _dbContext.StreamFilterTypeCode.Count(f => f.StreamId == updatedStream.Id).Should().Be(
                command.Request.Filters.TypeCodeIds.Count() + command.Request.ExclusionFilters?.TypeCodeIds?.Count());
        }

        if (command.Request.AdvancedSettings?.Amplifiers != null)
        {
            _dbContext.Amplifier.Count(a => a.StreamId == updatedStream.Id).Should()
                .Be(command.Request.AdvancedSettings.Amplifiers.Count());
        }

        if (command.Request.AdvancedSettings?.FewShotItemIds != null)
        {
            _dbContext.FewShot.Count(f => f.StreamId == updatedStream.Id).Should()
                .Be(command.Request.AdvancedSettings.FewShotItemIds.Count());
        }
    }

    private void SeedAmplifiers(StreamId streamId, int number = 3)
    {
        var amplifiers = _fixture.CreateMany<string>(number)
            .Select(name => Amplifier.Create(streamId, name, 0.5))
            .ToList();
        _dbContext.Amplifier.AddRange(amplifiers);
    }

    private void SeedFewShots(StreamId streamId, int number = 3)
    {
        var fewShots = _fixture.CreateMany<long>(number)
            .Select(itemId => FewShot.Create(streamId, itemId, 0.8))
            .ToList();
        _dbContext.FewShot.AddRange(fewShots);
    }

    private void SeedStreamFilters(StreamId streamId, int number = 3)
    {
        var streamFilters = _fixture.CreateMany<string>(3)
            .Select(filterValue => StreamMetadataFilter.Create(streamId, StreamMetadataFilterName.People, true, filterValue))
            .ToList();
        _dbContext.StreamMetadataFilter.AddRange(streamFilters);
    }

    private void SeedStreamFilterTypeCodes(StreamId streamId, int number = 2)
    {
        var streamFilterTypeCodes = _fixture.CreateMany<int>(number)
            .Select(typeCode => StreamFilterTypeCode.Create(typeCode, streamId, true))
            .ToList();
        _dbContext.StreamFilterTypeCode.AddRange(streamFilterTypeCodes);
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenStreamDoesNotExist()
    {
        // Arrange
        var command = _fixture.Create<UpdateStreamCommand>();

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Message
            .Contains($"The stream with id {command.Id} was not found"));
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenCompanyDoesNotExist()
    {
        // Arrange
        var existingStream = _fixture.Create<Stream>();
        _dbContext.Streams.Add(existingStream);
        await _dbContext.SaveChangesAsync();

        var command = _fixture.Build<UpdateStreamCommand>()
            .With(x => x.Id, existingStream.Id)
            .Create();

        _userApiClientMock.Setup(client => client.GetCompanyName(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Fail(new Error("Company not found")));

        // Act
        var result = await _handler.Handle(command, _cancellationToken);
        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Message.Contains("Company not found"));
    }

    [Fact]
    public async Task Handle_ShouldUpdateStream_WhenValidCommandIsProvided()
    {
        // Arrange
        var existingStream = _fixture.Create<Stream>();
        existingStream.IsDeleted = false;

        _dbContext.Streams.Add(existingStream);
        await _dbContext.SaveChangesAsync();

        var command = _fixture.Build<UpdateStreamCommand>()
            .With(x => x.Id, existingStream.Id)
            .With(x => x.IsMention, existingStream.IsMention)
            .Create();

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var updatedStream = await _dbContext.Streams.FirstOrDefaultAsync(s => s.Id == existingStream.Id);
        ValidateStream(command, updatedStream!);
        ValidateRelatedEntities(command, updatedStream!);
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenAddAmplifiersFails()
    {
        // Arrange
        var existingStream = _fixture.Create<Stream>();
        _dbContext.Streams.Add(existingStream);
        await _dbContext.SaveChangesAsync();

        var command = _fixture.Build<UpdateStreamCommand>()
            .With(x => x.Id, existingStream.Id)
            .Create();

        _colibriApiMock.Setup(api => api.AddAmplifiers(It.IsAny<AddAmplifiersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Fail(new Error("Amplifier error")));

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Message.Contains("Amplifier error"));
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenAddFewShotsFails()
    {
        // Arrange
        var existingStream = _fixture.Create<Stream>();
        _dbContext.Streams.Add(existingStream);
        await _dbContext.SaveChangesAsync();

        var command = _fixture.Build<UpdateStreamCommand>()
            .With(x => x.Id, existingStream.Id)
            .Create();

        _colibriApiMock.Setup(api => api.AddFewShots(It.IsAny<AddFewShotsRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Fail(new Error("FewShots error")));

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Message.Contains("FewShots error"));
    }

    [Fact]
    public async Task Handle_ShouldRemovePreviousAmplifiers_WhenNoAmplifiersProvided()
    {
        // Arrange
        var existingStream = _fixture.Create<Stream>();
        existingStream.IsDeleted = false;

        _dbContext.Streams.Add(existingStream);
        SeedAmplifiers(existingStream.Id, 3);
        await _dbContext.SaveChangesAsync();

        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, new AdvancedSettings([], 10, [1], 10))
            .Create();

        var command = new UpdateStreamCommand(existingStream.Id, request, false);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var updatedStream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, updatedStream!);
        ValidateRelatedEntities(command, updatedStream!);
        _dbContext.Amplifier.Count(a => a.StreamId == updatedStream!.Id).Should().Be(0);
    }

    [Fact]
    public async Task Handle_ShouldRemovePreviousFewShots_WhenNoFewShotsProvided()
    {
        // Arrange
        var existingStream = _fixture.Create<Stream>();
        existingStream.IsDeleted = false;

        _dbContext.Streams.Add(existingStream);
        SeedFewShots(existingStream.Id, 3);
        await _dbContext.SaveChangesAsync();

        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, new AdvancedSettings(["Amplifier"], 10, [], 0))
            .Create();

        var command = new UpdateStreamCommand(existingStream.Id, request, false);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var updatedStream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, updatedStream!);
        ValidateRelatedEntities(command, updatedStream!);
        _dbContext.FewShot.Count(f => f.StreamId == updatedStream!.Id).Should().Be(0);
    }

    [Fact]
    public async Task Handle_ShouldRemovePreviousAmplifiersAndFewShots_WhenNoAmplifiersAndFewShotsProvided()
    {
        // Arrange
        var existingStream = _fixture.Create<Stream>();
        existingStream.IsDeleted = false;

        _dbContext.Streams.Add(existingStream);

        SeedAmplifiers(existingStream.Id, 3);
        SeedFewShots(existingStream.Id, 3);
        await _dbContext.SaveChangesAsync();

        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.AdvancedSettings, new AdvancedSettings([], 0, [], 0))
            .Create();

        var command = new UpdateStreamCommand(existingStream.Id, request, false);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);
        // Assert
        result.IsSuccess.Should().BeTrue();
        var updatedStream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, updatedStream!);
        ValidateRelatedEntities(command, updatedStream!);
        _dbContext.Amplifier.Count(a => a.StreamId == updatedStream!.Id).Should().Be(0);
        _dbContext.FewShot.Count(f => f.StreamId == updatedStream!.Id).Should().Be(0);
    }

    [Fact]
    public async Task Handle_ShouldRemovePreviousStreamFilters_WhenNoFiltersProvided()
    {
        // Arrange
        var existingStream = _fixture.Create<Stream>();
        existingStream.IsDeleted = false;

        _dbContext.Streams.Add(existingStream);
        SeedStreamFilters(existingStream.Id, 3);
        await _dbContext.SaveChangesAsync();

        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Filters, new Filters([], [], [], [], []))
            .With(x => x.ExclusionFilters, new ExclusionFilters([], [], [], [], []))
            .Create();

        var command = new UpdateStreamCommand(existingStream.Id, request, false);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);
        // Assert
        result.IsSuccess.Should().BeTrue();
        var updatedStream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, updatedStream!);
        ValidateRelatedEntities(command, updatedStream!);
        _dbContext.StreamMetadataFilter.Count(f => f.StreamId == updatedStream!.Id).Should().Be(0);
    }

    [Fact]
    public async Task Handle_ShouldRemovePreviousStreamFilterTypeCodes_WhenNoTypeCodesProvided()
    {
        // Arrange
        var existingStream = _fixture.Create<Stream>();
        existingStream.IsDeleted = false;

        _dbContext.Streams.Add(existingStream);
        SeedStreamFilterTypeCodes(existingStream.Id, 3);
        await _dbContext.SaveChangesAsync();

        var request = _fixture.Build<UpdateRequestResponse>()
            .With(x => x.Filters, new Filters([], [], [], [], []))
            .With(x => x.ExclusionFilters, new ExclusionFilters([], [], [], [], []))
            .Create();

        var command = new UpdateStreamCommand(existingStream.Id, request, false);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);
        // Assert
        result.IsSuccess.Should().BeTrue();
        var updatedStream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, updatedStream!);
        ValidateRelatedEntities(command, updatedStream!);
        _dbContext.StreamFilterTypeCode.Count(f => f.StreamId == updatedStream!.Id).Should().Be(0);
    }
}
