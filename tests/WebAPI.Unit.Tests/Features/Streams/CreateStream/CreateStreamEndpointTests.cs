using System.Net;
using AutoFixture;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using WebAPI.Features.Streams.CreateStream;
using WebAPI.Features.Streams.Shared;

namespace WebAPI.Unit.Tests.Features.Streams.CreateStream;

public class CreateStreamEndpointTests
{
    private readonly Mock<ISender> _senderMock;
    private readonly CreateStreamEndpoint _endpoint;
    private readonly Fixture _fixture;

    public CreateStreamEndpointTests()
    {
        _senderMock = new Mock<ISender>();
        _endpoint = new CreateStreamEndpoint(_senderMock.Object);
        _fixture = new Fixture();
    }

    [Fact]
    public async Task HandleAsync_WhenCommandSucceeds_ReturnsCreatedResult()
    {
        // Arrange
        var request = _fixture.Create<CreateRequestResponse>();
        var response = _fixture.Create<Response>();
        _senderMock.Setup(s => s.Send(It.IsAny<CreateStreamCommand>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(Result.Ok(response));

        // Act
        var result = await _endpoint.HandleAsync(request);

        // Assert
        _senderMock.Verify(sender => sender.Send(It.Is<CreateStreamCommand>(c =>
                        c.Request.Name == request.Name &&
                        c.Request.CompanyId == request.CompanyId &&
                        c.Request.Prompt == request.Prompt &&
                        c.Request.AiGeneratedPrompt == request.AiGeneratedPrompt &&
                        c.Request.Filters == request.Filters &&
                        c.Request.ExclusionFilters == request.ExclusionFilters &&
                        c.Request.AiParameters == request.AiParameters &&
                        c.Request.AdvancedSettings == request.AdvancedSettings),
                        CancellationToken.None), Times.Once);

        result.Should().BeOfType<ActionResult<Response>>();
        result.Result.Should().BeOfType<CreatedResult>();
    }

    [Fact]
    public async Task HandleAsync_WhenCommandFails_ReturnsBadRequestWithProblemDetails()
    {
        // Arrange
        var request = _fixture.Create<CreateRequestResponse>();
        var failureResult = Result.Fail("Error occurred");
        _senderMock.Setup(s => s.Send(It.IsAny<CreateStreamCommand>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(failureResult);

        // Act
        var result = await _endpoint.HandleAsync(request);

        // Assert
        _senderMock.Verify(sender => sender.Send(It.Is<CreateStreamCommand>(c =>
                        c.Request.Name == request.Name &&
                        c.Request.CompanyId == request.CompanyId &&
                        c.Request.Prompt == request.Prompt &&
                        c.Request.AiGeneratedPrompt == request.AiGeneratedPrompt &&
                        c.Request.Filters == request.Filters &&
                        c.Request.ExclusionFilters == request.ExclusionFilters &&
                        c.Request.AiParameters == request.AiParameters &&
                        c.Request.AdvancedSettings == request.AdvancedSettings),
                        CancellationToken.None), Times.Once);

        result.Should().BeOfType<ActionResult<Response>>();
        result.Result.Should().BeOfType<BadRequestObjectResult>();
    }
}
