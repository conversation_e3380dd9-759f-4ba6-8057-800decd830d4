using AutoFixture;
using FluentValidation.TestHelper;
using WebAPI.Features.Streams.CreateStream;
using WebAPI.Features.Streams.Shared;

namespace WebAPI.Unit.Tests.Features.Streams.CreateStream;

public class CreateStreamRequestValidatorTests
{
    private readonly CreateStreamRequestValidator _validator = new();
    private readonly Fixture _fixture = new();

    [Fact]
    public void CreateStreamRequestValidator_WhenValidRequest_ShouldNotHaveValidationError()
    {
        // Arrange
        var createRequest = _fixture.Create<CreateStreamRequest>();

        // Act
        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenNameIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Name, null as string)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldHaveValidationErrorFor(x => x.Request.Name)
            .WithErrorMessage("'Request Name' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenNameIsEmpty_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Name, string.Empty)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldHaveValidationErrorFor(x => x.Request.Name)
            .WithErrorMessage("'Request Name' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenPromptIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Prompt, null as string)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldHaveValidationErrorFor(x => x.Request.Prompt)
            .WithErrorMessage("'Request Prompt' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenPromptIsEmpty_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Prompt, string.Empty)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldHaveValidationErrorFor(x => x.Request.Prompt)
            .WithErrorMessage("'Request Prompt' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenFiltersIsNullAndIsMention_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Filters, null as Filters)
            .With(x => x.IsMention, true)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldHaveValidationErrorFor(x => x.Request.Filters)
            .WithErrorMessage("'Request Filters' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenFiltersIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Filters, null as Filters)
            .With(x => x.IsMention, false)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldNotHaveValidationErrorFor(x => x.Request.Filters);
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenFiltersPropertiesIsEmpty_ShouldNotHaveAnyValidationError()
    {
        var emptyFilters = new Filters([], [], [], [], []);
        var request = _fixture.Build<CreateRequestResponse>().With(x => x.Filters, emptyFilters).Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenFiltersTypeCodeIdsIsNull_ShouldHaveValidationError()
    {
        var filters = new Filters([], [], [], [], null);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Filters, filters)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldHaveValidationErrorFor(x => x.Request.Filters.TypeCodeIds)
            .WithErrorMessage("'Filters Type Code Ids' must not be null.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenExclusionFiltersIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.ExclusionFilters, null as ExclusionFilters)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters)
            .WithErrorMessage("'Request Exclusion Filters' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenExclusionFiltersPropertiesIsEmpty_ShouldNotHaveAnyValidationError()
    {
        // Arrange
        var emptyExclusionFilters = new ExclusionFilters([], [], [], [], []);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.ExclusionFilters, emptyExclusionFilters)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenExclusionFiltersPeopleIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters(null, [], [], [], []);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.People)
            .WithErrorMessage("'Exclusion Filters People' must not be null.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenExclusionFiltersOrganisationIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters([], null, [], [], []);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.Organisations)
            .WithErrorMessage("'Exclusion Filters Organisation' must not be null.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenExclusionFiltersProgrammeIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters([], [], null, [], []);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.Programmes)
            .WithErrorMessage("'Exclusion Filters Programme' must not be null.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenExclusionFiltersThemeIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters([], [], [], null, []);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.Themes)
            .WithErrorMessage("'Exclusion Filters Themes' must not be null.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenExclusionFiltersTypeCodeIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var exclusionFilters = new ExclusionFilters([], [], [], [], null);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.ExclusionFilters, exclusionFilters)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.ExclusionFilters.TypeCodeIds)
            .WithErrorMessage("'Exclusion Filters Type Code Ids' must not be null.");
    }


    [Fact]
    public void CreateStreamRequestValidator_WhenAiGeneratedPromptIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AiGeneratedPrompt, null as string)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldHaveValidationErrorFor(x => x.Request.AiGeneratedPrompt)
            .WithErrorMessage("'Request Ai Generated Prompt' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAiGeneratedPromptIsEmpty_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AiGeneratedPrompt, string.Empty)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldHaveValidationErrorFor(x => x.Request.AiGeneratedPrompt)
            .WithErrorMessage("'Request Ai Generated Prompt' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAiParametersIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AiParameters, null as AiParameters)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AiParameters)
            .WithErrorMessage("'Request Ai Parameters' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAiParametersAiModelIdsIsEmpty_ShouldHaveValidationError()
    {
        // Arrange
        var aiParameters = new AiParameters([], 1);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AiParameters, aiParameters)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AiParameters.AiModelIds)
            .WithErrorMessage("'Request Ai Parameters Ai Model Ids' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAiParametersAiModelIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var aiParameters = new AiParameters(null, 1);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AiParameters, aiParameters)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AiParameters.AiModelIds)
            .WithErrorMessage("'Request Ai Parameters Ai Model Ids' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAiParametersConfidenceIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var aiParameters = new AiParameters([Guid.NewGuid()], default);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AiParameters, aiParameters)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AiParameters.Confidence)
            .WithErrorMessage("'Request Ai Parameters Confidence' must not be empty.");
    }


    [Fact]
    public void CreateStreamRequestValidator_WhenAdvancedSettingsIsNull_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, null as AdvancedSettings)
            .Create();

        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("'Request Advanced Settings' must not be empty.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAdvancedSettingsAmplifiersIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings(null, 10, [], 5);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings.Amplifiers)
            .WithErrorMessage("'Advanced Settings Amplifiers' must not be null.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAdvancedSettingsAmplifierRelevancyIsNull_ShouldNotHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], null, [], 5);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();
        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);
        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings.AmplifierRelevancy)
            .WithErrorMessage("'Advanced Settings Amplifier Relevancy' must not be null.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAdvancedSettingsFewShotItemIdsIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 10, null, 5);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();
        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);
        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings.FewShotItemIds)
            .WithErrorMessage("'Few Shot Item Ids' must not be null.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAdvancedSettingsFewShotRelevancyIsNull_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 10, [], null);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();
        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);
        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings.FewShotRelevancy)
            .WithErrorMessage("'Few Shot Relevancy' must not be null.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAmplifiersHasValueAndAmplifierRelevancyIsZero_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings(["Amplifiers"], 0, [1], 10);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("If Amplifiers has value, Amplifier Relevancy must be greater than zero.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAmplifiersRelevencyIsNotZeroAndAmplifiersIsEmpty_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 10, [1], 10);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("If Amplifiers has value, Amplifier Relevancy must be greater than zero.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenAmplifiersIsEmptyAndAmplifierRelevancyIsZero_ShouldNotHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 0, [1], 10);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenFewShotItemIdsHasValueAndFewShotRelevancyIsZero_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings(["Amplifier"], 10, [1, 2, 3], 0);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("If Few Shot Item Id's has value, Few Shot Relevancy must be greater than zero.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenFewShotRelevancyIsNotZeroAndFewShotItemIdsIsEmpty_ShouldHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings(["Amplifier"], 10, [], 10);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();

        // Act
        var createRequest = new CreateStreamRequest(request);

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Request.AdvancedSettings)
            .WithErrorMessage("If Few Shot Item Id's has value, Few Shot Relevancy must be greater than zero.");
    }

    [Fact]
    public void CreateStreamRequestValidator_WhenFewShotItemIdsIsEmptyAndFewShotRelevancyIsZero_ShouldNotHaveValidationError()
    {
        // Arrange
        var advancedSettings = new AdvancedSettings([], 0, [], 0);
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, advancedSettings)
            .Create();
        var createRequest = new CreateStreamRequest(request);

        // Act

        var result = _validator.TestValidate(createRequest);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionIsTrue_ShouldNotHaveValidationErrorForNullPrompt()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.IsMention, true)
            .With(x => x.Prompt, null as string)
            .Create();

        var command = new CreateStreamRequest(request);

        var result = _validator.TestValidate(command);

        result.ShouldNotHaveValidationErrorFor(x => x.Request.Prompt);
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionIsTrue_ShouldHaveValidationErrorForPrompt()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.IsMention, true)
            .With(x => x.Prompt, "Prompt")
            .Create();

        var command = new CreateStreamRequest(request);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Prompt);
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionAndFiltersHasNoPopulatedProperties_ShouldHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Filters, new Filters([], [], [], [], []))
            .With(x => x.IsMention, true)
            .Create();

        var command = new CreateStreamRequest(request);

        var result = _validator.TestValidate(command);

        result.ShouldHaveValidationErrorFor(x => x.Request.Filters)
            .WithErrorMessage("At least one of People, Organisation, or Programme must contain a filter.");
    }

    [Fact]
    public void CreateStreamCommandValidator_WhenIsMentionAndFiltersHasPopulatedProperties_ShouldNotHaveValidationError()
    {
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Filters, new Filters([new Filter(Guid.NewGuid(), "Boris Johnson")], [], [], [], []))
            .With(x => x.IsMention, true)
            .Create();

        var command = new CreateStreamRequest(request);

        var result = _validator.TestValidate(command);

        result.ShouldNotHaveValidationErrorFor(x => x.Request.Filters);
    }
}
