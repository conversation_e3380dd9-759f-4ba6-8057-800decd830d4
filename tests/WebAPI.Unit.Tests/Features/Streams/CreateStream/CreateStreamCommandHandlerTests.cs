using Apollo.Application.Unit.Tests.Common;
using AutoFixture;
using FluentResults;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.AddAmplifiers;
using SharedKernel.Services.ColibriApi.AddFewShots;
using SharedKernel.Services.UserApi;
using WebAPI.Features.Streams.CreateStream;
using WebAPI.Features.Streams.Shared;
using static SharedKernel.Services.UserApi.UserApiClient;
using Stream = SharedKernel.Entities.Stream;

namespace WebAPI.Unit.Tests.Features.Streams.CreateStream;

public class CreateStreamCommandHandlerTests : HandlerTestBase
{
    private readonly Mock<IColibriApi> _colibriApiMock;
    private readonly Mock<ILogger<CreateStreamCommandHandler>> _loggerMock;
    private readonly CreateStreamCommandHandler _handler;
    private readonly Mock<IUserApiClient> _userApiClientMock;

    public CreateStreamCommandHandlerTests()
    {
        _colibriApiMock = new Mock<IColibriApi>();

        _loggerMock = new Mock<ILogger<CreateStreamCommandHandler>>();
        _userApiClientMock = new Mock<IUserApiClient>();
        _handler = new CreateStreamCommandHandler(_dbContext, _userApiClientMock.Object, _colibriApiMock.Object,
            _loggerMock.Object);

        MockNecessaryMethods();
    }

    private void MockNecessaryMethods()
    {
        _colibriApiMock.Setup(api => api.AddAmplifiers(It.IsAny<AddAmplifiersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok(new AddAmplifiersResponse { ErrorAmplifiers = [] }));
        _colibriApiMock.Setup(api => api.AddFewShots(It.IsAny<AddFewShotsRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok(new AddFewShotsResponse { ErrorFewShots = [] }));
        _userApiClientMock.Setup(client => client.GetCompanyName(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok(new CompanyResponse("Test Company")));
    }

    private static void ValidateStream(CreateStreamCommand command, Stream stream)
    {
        stream.Should().NotBeNull();
        stream.Should().BeEquivalentTo(command, options => options.ExcludingMissingMembers());
    }

    private void ValidateRelatedEntities(CreateStreamCommand command, Stream createdStream)
    {
        if (command.Request.Filters != null)
        {
            var expectedFilterCount = (command.Request.Filters.People?.Count() ?? 0) +
                                      (command.Request.Filters.Organisations?.Count() ?? 0) +
                                      (command.Request.Filters.Programmes?.Count() ?? 0) +
                                      (command.Request.Filters.Themes?.Count() ?? 0) +
                                      (command.Request.ExclusionFilters?.People?.Count() ?? 0) +
                                      (command.Request.ExclusionFilters?.Organisations?.Count() ?? 0) +
                                      (command.Request.ExclusionFilters?.Programmes?.Count() ?? 0) +
                                      (command.Request.ExclusionFilters?.Themes?.Count() ?? 0);
            _dbContext.StreamMetadataFilter.Count(f => f.StreamId == createdStream.Id).Should().Be(expectedFilterCount);
        }

        if (command.Request.Filters?.TypeCodeIds != null && command.Request.ExclusionFilters?.TypeCodeIds != null)
        {
            _dbContext.StreamFilterTypeCode.Count(f => f.StreamId == createdStream.Id).Should().Be(
                command.Request.Filters.TypeCodeIds.Count() + command.Request.ExclusionFilters.TypeCodeIds.Count());
        }

        if (command.Request.AdvancedSettings?.Amplifiers != null)
        {
            _dbContext.Amplifier.Count(a => a.StreamId == createdStream.Id).Should()
                .Be(command.Request.AdvancedSettings.Amplifiers.Count());
        }

        if (command.Request.AdvancedSettings?.FewShotItemIds != null)
        {
            _dbContext.FewShot.Count(f => f.StreamId == createdStream.Id).Should()
                .Be(command.Request.AdvancedSettings.FewShotItemIds.Count());
        }
    }


    [Fact]
    public async Task Handle_ShouldCreateStream_WhenValidCommandIsProvided()
    {
        // Arrange
        var command = _fixture.Create<CreateStreamCommand>();

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var stream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, stream!);
        ValidateRelatedEntities(command, stream!);
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenAddAmplifiersFails()
    {
        // Arrange
        var command = _fixture.Create<CreateStreamCommand>();
        _colibriApiMock.Setup(api => api.AddAmplifiers(It.IsAny<AddAmplifiersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Fail(new Error("Amplifier error")));

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Message.Contains("Amplifier error"));
        _dbContext.Streams.FirstOrDefault().Should().BeNull();
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenAddFewShotsFails()
    {
        // Arrange
        var command = _fixture.Create<CreateStreamCommand>();
        _colibriApiMock.Setup(api => api.AddFewShots(It.IsAny<AddFewShotsRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Fail(new Error("FewShots error")));

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Message.Contains("FewShots error"));
        _dbContext.Streams.Should().BeEmpty();
        _dbContext.StreamMetadataFilter.Should().BeEmpty();
    }

    [Fact]
    public async Task Handle_ShouldCreateStreamWithoutAmplifiers_WhenNoAmplifiersProvided()
    {
        // Arrange
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, new AdvancedSettings([], 10, [1], 10))
            .Create();
        var command = new CreateStreamCommand(request);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var stream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, stream!);
        ValidateRelatedEntities(command, stream!);
    }

    [Fact]
    public async Task Handle_ShouldCreateStreamWithoutFewShots_WhenNoFewShotsProvided()
    {
        // Arrange
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, new AdvancedSettings(["Amplifier"], 10, [], 0))
            .Create();
        var command = new CreateStreamCommand(request);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var stream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, stream!);
        ValidateRelatedEntities(command, stream!);
    }

    [Fact]
    public async Task Handle_ShouldCreateStreamWithoutAmplifiersAndFewShots_WhenNoAmplifiersAndFewShotsProvided()
    {
        // Arrange
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.AdvancedSettings, new AdvancedSettings([], 0, [], 0))
            .Create();
        var command = new CreateStreamCommand(request);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var stream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, stream!);
        ValidateRelatedEntities(command, stream!);
    }

    [Fact]
    public async Task Handle_ShouldHandleEmptyFiltersCorrectly()
    {
        // Arrange
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.Filters, new Filters([], [], [], [], []))
            .With(x => x.ExclusionFilters, new ExclusionFilters([], [], [], [], []))
            .Create();
        var command = new CreateStreamCommand(request);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var streamFilters = await _dbContext.StreamMetadataFilter.ToListAsync();
        var stream = await _dbContext.Streams.FirstOrDefaultAsync();
        ValidateStream(command, stream!);
        ValidateRelatedEntities(command, stream!);
    }

    [Fact]
    public async Task Handle_ShouldReturnCompanyNameWhenCompanyIdIsSet()
    {
        // Arrange
        var request = _fixture.Build<CreateRequestResponse>()
            .With(x => x.CompanyId, Guid.NewGuid())
            .Create();

        var command = new CreateStreamCommand(request);

        _userApiClientMock.Setup(client => client.GetCompanyName(request.CompanyId!.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok(new CompanyResponse("Test Company")));

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var stream = await _dbContext.Streams.FirstOrDefaultAsync(x => x.Id.Value == result.Value.Id);
        stream.Should().NotBeNull();
        stream!.CompanyName.Should().Be("Test Company");
    }
}
