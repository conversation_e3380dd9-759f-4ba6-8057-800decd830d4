using FluentResults;
using Microsoft.EntityFrameworkCore;
using Moq;
using SharedKernel.Entities;
using SharedKernel.Services;
using WebAPI.Features.Streams.CreateFeed;
using WebAPI.Persistence;
using AiModel = SharedKernel.Entities.AiModel;
using Stream = SharedKernel.Entities.Stream;

namespace WebAPI.Unit.Tests.Features.Streams.CreateFeed;

public class CreateFeedCommandHandlerTests : IDisposable
{
    private readonly Mock<ICurrentUserService> _currentUserServiceMock;
    private readonly Mock<IDateTimeService> _dateTimeServiceMock;
    private readonly ApplicationDbContext _dbContext;
    private readonly CreateFeedCommandHandler _handler;
    private readonly CancellationToken _cancellationToken = CancellationToken.None;

    public CreateFeedCommandHandlerTests()
    {
        _currentUserServiceMock = new Mock<ICurrentUserService>();
        _dateTimeServiceMock = new Mock<IDateTimeService>();
        
        // Create database context with our mock services
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
               .UseInMemoryDatabase(Guid.NewGuid().ToString())
               .Options;

        _dateTimeServiceMock.Setup(x => x.UtcNow).Returns(DateTimeOffset.UtcNow);
        _currentUserServiceMock.Setup(x => x.IdentityId).Returns("00000000-0000-0000-0000-000000000000");

        _dbContext = new ApplicationDbContext(options, _dateTimeServiceMock.Object, _currentUserServiceMock.Object);
        _dbContext.Database.EnsureCreated();
        
        _handler = new CreateFeedCommandHandler(_dbContext, _currentUserServiceMock.Object);
    }

    public void Dispose()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }

    [Fact]
    public async Task Handle_WhenUserIdIsEmpty_ReturnsFailure()
    {
        // Arrange
        _currentUserServiceMock.Setup(x => x.Id).Returns(string.Empty);
        var request = new CreateFeedRequest(
            People: new[] { "John Doe" },
            Programmes: new[] { "Test Programme" },
            Themes: new[] { "Test Theme" },
            Organisations: new[] { "Test Org" }
        );
        var command = new CreateFeedCommand(request);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.Message.Contains("User ID not found"));
    }

    [Fact]
    public async Task Handle_WhenNoAiModelExists_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);
        
        var request = new CreateFeedRequest(
            People: new[] { "John Doe" },
            Programmes: new[] { "Test Programme" },
            Themes: new[] { "Test Theme" },
            Organisations: new[] { "Test Org" }
        );
        var command = new CreateFeedCommand(request);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.Message.Contains("No AI model found"));
    }

    [Fact]
    public async Task Handle_WhenCreatingNewFeed_ReturnsSuccessWithStreamId()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);

        var aiModel = AiModel.Create("Test AI Model");
        _dbContext.AiModels.Add(aiModel);
        await _dbContext.SaveChangesAsync();

        var request = new CreateFeedRequest(
            People: new[] { "John Doe", "Jane Smith" },
            Programmes: new[] { "Test Programme" },
            Themes: new[] { "Test Theme" },
            Organisations: new[] { "Test Org" }
        );
        var command = new CreateFeedCommand(request);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.StreamId.Should().NotBeEmpty();
        result.Value.Message.Should().Be("Feed stream created successfully");

        // Verify stream was created
        var createdStream = await _dbContext.Streams
            .Include(s => s.StreamMetadataFilters)
            .FirstOrDefaultAsync(s => s.Id.Value == result.Value.StreamId);
        
        createdStream.Should().NotBeNull();
        createdStream!.IsFeed.Should().BeTrue();
        createdStream.Name.Should().Be($"User Feed - {userId}");
        createdStream.CreatedBy.Should().Be(userId);
        
        // Verify metadata filters were created
        createdStream.StreamMetadataFilters.Should().HaveCount(5); // 2 people + 1 programme + 1 theme + 1 org
        createdStream.StreamMetadataFilters.Should().Contain(f => f.FilterValue == "John Doe" && f.FilterName == "People");
        createdStream.StreamMetadataFilters.Should().Contain(f => f.FilterValue == "Jane Smith" && f.FilterName == "People");
        createdStream.StreamMetadataFilters.Should().Contain(f => f.FilterValue == "Test Programme" && f.FilterName == "Programme");
        createdStream.StreamMetadataFilters.Should().Contain(f => f.FilterValue == "Test Theme" && f.FilterName == "Theme");
        createdStream.StreamMetadataFilters.Should().Contain(f => f.FilterValue == "Test Org" && f.FilterName == "Organisation");
    }

    [Fact]
    public async Task Handle_WhenUpdatingExistingFeed_ReturnsSuccessWithUpdatedFilters()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);

        var aiModel = AiModel.Create("Test AI Model");
        _dbContext.AiModels.Add(aiModel);

        // Create existing feed stream
        var existingStream = Stream.Create(
            companyId: null,
            aiModelId: aiModel.Id,
            name: $"User Feed - {userId}",
            prompt: string.Empty,
            companyName: string.Empty,
            companySpecificStream: null,
            aiGeneratedPrompt: string.Empty,
            aiGeneratedPromptId: -1,
            aiModelConfidence: 0.9
        );

        // Set IsFeed to true and CreatedBy
        existingStream.GetType().GetProperty("IsFeed")?.SetValue(existingStream, true);
        existingStream.CreatedBy = userId;

        // Add existing metadata filters
        var existingFilters = new List<StreamMetadataFilter>
        {
            StreamMetadataFilter.Create(existingStream.Id, StreamMetadataFilterName.People, true, "Old Person"),
            StreamMetadataFilter.Create(existingStream.Id, StreamMetadataFilterName.Organisation, true, "Old Org")
        };

        _dbContext.Streams.Add(existingStream);
        _dbContext.StreamMetadataFilter.AddRange(existingFilters);
        await _dbContext.SaveChangesAsync();

        var request = new CreateFeedRequest(
            People: new[] { "New Person" },
            Programmes: new[] { "New Programme" },
            Themes: new[] { "New Theme" },
            Organisations: new[] { "New Org" }
        );
        var command = new CreateFeedCommand(request);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.StreamId.Should().Be(existingStream.Id.Value);
        result.Value.Message.Should().Be("Feed stream updated successfully");

        // Verify stream was updated
        var updatedStream = await _dbContext.Streams
            .Include(s => s.StreamMetadataFilters)
            .FirstOrDefaultAsync(s => s.Id == existingStream.Id);
        
        updatedStream.Should().NotBeNull();
        updatedStream!.IsFeed.Should().BeTrue();
        
        // Verify old filters were removed and new ones added
        updatedStream.StreamMetadataFilters.Should().HaveCount(4); // 1 person + 1 programme + 1 theme + 1 org
        updatedStream.StreamMetadataFilters.Should().NotContain(f => f.FilterValue == "Old Person");
        updatedStream.StreamMetadataFilters.Should().NotContain(f => f.FilterValue == "Old Org");
        updatedStream.StreamMetadataFilters.Should().Contain(f => f.FilterValue == "New Person" && f.FilterName == "People");
        updatedStream.StreamMetadataFilters.Should().Contain(f => f.FilterValue == "New Programme" && f.FilterName == "Programme");
        updatedStream.StreamMetadataFilters.Should().Contain(f => f.FilterValue == "New Theme" && f.FilterName == "Theme");
        updatedStream.StreamMetadataFilters.Should().Contain(f => f.FilterValue == "New Org" && f.FilterName == "Organisation");
    }

    [Fact]
    public async Task Handle_WhenEmptyArraysProvided_CreatesStreamWithoutFilters()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);

        var aiModel = AiModel.Create("Test AI Model");
        _dbContext.AiModels.Add(aiModel);
        await _dbContext.SaveChangesAsync();

        var request = new CreateFeedRequest(
            People: Array.Empty<string>(),
            Programmes: Array.Empty<string>(),
            Themes: Array.Empty<string>(),
            Organisations: Array.Empty<string>()
        );
        var command = new CreateFeedCommand(request);

        // Act
        var result = await _handler.Handle(command, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.StreamId.Should().NotBeEmpty();
        result.Value.Message.Should().Be("Feed stream created successfully");

        // Verify stream was created without filters
        var createdStream = await _dbContext.Streams
            .Include(s => s.StreamMetadataFilters)
            .FirstOrDefaultAsync(s => s.Id.Value == result.Value.StreamId);
        
        createdStream.Should().NotBeNull();
        createdStream!.IsFeed.Should().BeTrue();
        createdStream.StreamMetadataFilters.Should().BeEmpty();
    }
}
