using AutoFixture;
using FluentValidation.TestHelper;
using SharedKernel.Entities;
using WebAPI.Features.Streams.GetIsMentionStream;

namespace WebAPI.Unit.Tests.Features.Streams.GetIsMentionStream;

public class GetIsMentionStreamQueryValidatorTests
{
    private readonly GetIsMentionStreamQueryValidator _validator;
    private readonly Fixture _fixture;

    public GetIsMentionStreamQueryValidatorTests()
    {
        _validator = new GetIsMentionStreamQueryValidator();
        _fixture = new Fixture();
    }

    [Fact]
    public void GetIsMentionStreamQueryValidator_WhenStreamIdIsEmpty_ReturnsFailure()
    {
        // Arrange
        var query = _fixture.Build<GetIsMentionStreamQuery>()
            .With(q => q.StreamId, new StreamId(Guid.Empty))
            .Create();
        // Act
        var result = _validator.TestValidate(query);
        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(q => q.StreamId);
    }

    [Fact]
    public void GetIsMentionStreamQueryValidator_WhenStreamIdIsNotEmpty_ReturnsSuccess()
    {
        // Arrange
        var query = _fixture.Create<GetIsMentionStreamQuery>();
        // Act
        var result = _validator.TestValidate(query);
        // Assert
        result.IsValid.Should().BeTrue();
    }
}
