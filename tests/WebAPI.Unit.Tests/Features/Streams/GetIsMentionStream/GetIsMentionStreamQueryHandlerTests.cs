using Apollo.Application.Unit.Tests.Common;
using SharedKernel.Entities;
using WebAPI.Features.Streams.GetIsMentionStream;
using Stream = SharedKernel.Entities.Stream;

namespace WebAPI.Unit.Tests.Features.Streams.GetIsMentionStream;

public class GetIsMentionStreamQueryHandlerTests : HandlerTestBase
{
    private readonly GetIsMentionStreamQueryHandler _handler;

    public GetIsMentionStreamQueryHandlerTests()
    {
        _handler = new GetIsMentionStreamQueryHandler(_dbContext);
    }

    [Fact]
    public async Task Handle_ShouldReturnIsMention_WhenStreamExists()
    {
        // Arrange
        var stream = Stream.CreateMention("Test Stream", null, "", null);

        _dbContext.Streams.Add(stream);
        await _dbContext.SaveChangesAsync();

        var query = new GetIsMentionStreamQuery(stream.Id);

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.IsMention.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenStreamNotFound()
    {
        // Arrange
        var query = new GetIsMentionStreamQuery(new StreamId(Guid.NewGuid()));

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.Message.Contains("not found"));
    }
}
