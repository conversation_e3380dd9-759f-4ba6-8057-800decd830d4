using Apollo.Application.Unit.Tests.Common;
using AutoFixture;
using WebAPI.Features.Streams.GetStreams;
using WebAPI.Infrastructure.Enums;
using Stream = SharedKernel.Entities.Stream;

namespace WebAPI.Unit.Tests.Features.Streams.GetStreams;

public class GetStreamsQueryHandlerTests : HandlerTestBase
{
    private readonly GetStreamsQueryHandler _handler;

    public GetStreamsQueryHandlerTests()
    {
        _handler = new GetStreamsQueryHandler(_dbContext);
    }

    [Fact]
    public async Task Handle_ReturnsPaginatedStreams_WhenQueryIsValid()
    {
        // Arrange
        var query = _fixture.Build<GetStreamsQuery>()
            .With(q => q.PageSize, 10)
            .With(q => q.PageNumber, 1)
            .With(q => q.Search, null as String)
            .With(q => q.CompanyId, null as Guid?)
            .With(q => q.UserId, null as Guid?)
            .Create();

        var streamsList = _fixture.CreateMany<Stream>(5).ToList();
        SetIsDeletedFalse(streamsList);

        await _dbContext.Streams.AddRangeAsync(streamsList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(result.Value.TotalRecords, streamsList.Count());
        Assert.Equal(result.Value.Count, streamsList.Count());
    }

    [Theory]
    [InlineData(3, 1, 10, 3)]
    [InlineData(3, 2, 10, 3)]
    [InlineData(5, 1, 10, 5)]
    [InlineData(5, 2, 10, 5)]
    [InlineData(10, 1, 15, 10)]
    [InlineData(5, 2, 8, 3)]
    public async Task Handle_ReturnsCorrectNumberOfStreams_OnSpecificPage(int pageSize, int pageNumber, int totalStreams, int expectedItemCount)
    {
        // Arrange
        var query = _fixture.Build<GetStreamsQuery>()
            .With(q => q.PageSize, pageSize)
            .With(q => q.PageNumber, pageNumber)
            .With(q => q.Search, null as String)
            .With(q => q.CompanyId, null as Guid?)
            .With(q => q.UserId, null as Guid?)
            .Create();

        var streamsList = _fixture.CreateMany<Stream>(totalStreams).ToList();
        SetIsDeletedFalse(streamsList);

        await _dbContext.Streams.AddRangeAsync(streamsList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(streamsList.Count, result.Value.TotalRecords);
        Assert.Equal(expectedItemCount, result.Value.Count);
    }

    [Theory]
    [InlineData(StreamSortProperty.Name, PropertySortType.ASC)]
    [InlineData(StreamSortProperty.Name, PropertySortType.DESC)]
    [InlineData(StreamSortProperty.LastUpdated, PropertySortType.ASC)]
    [InlineData(StreamSortProperty.LastUpdated, PropertySortType.DESC)]
    public async Task Handle_AppliesSortingCorrectly_WhenSortPropertiesAreProvided(
    StreamSortProperty sortProperty,
    PropertySortType sortType)
    {
        // Arrange
        var query = _fixture.Build<GetStreamsQuery>()
            .With(q => q.PageSize, 10)
            .With(q => q.PageNumber, 1)
            .With(q => q.SortProperty, sortProperty)
            .With(q => q.SortType, sortType)
            .With(q => q.Search, null as String)
            .With(q => q.CompanyId, null as Guid?)
            .With(q => q.UserId, null as Guid?)
            .Create();

        var streamsList = _fixture.CreateMany<Stream>(5).ToList();
        SetIsDeletedFalse(streamsList);

        await _dbContext.Streams.AddRangeAsync(streamsList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);

        IOrderedEnumerable<Stream> sortedStreams;
        if (sortProperty == StreamSortProperty.Name)
        {
            sortedStreams = sortType == PropertySortType.ASC
                ? streamsList.OrderBy(s => s.Name)
                : streamsList.OrderByDescending(s => s.Name);
        }
        else
        {
            sortedStreams = sortType == PropertySortType.ASC
                ? streamsList.OrderBy(s => s.UpdatedDateUtc)
                : streamsList.OrderByDescending(s => s.UpdatedDateUtc);
        }

        Assert.Equal(
            sortedStreams.Select(s => s.Name).ToList(),
            result.Value.Select(r => r.Name).ToList());

        Assert.Equal(
            sortedStreams.Select(s => s.UpdatedDateUtc).ToList(),
            result.Value.Select(r => r.LastUpdatedUtc).ToList());

        Assert.Equal(
            sortedStreams.Select(s => s.Id.Value).ToList(),
            result.Value.Select(r => r.Id).ToList());

    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenNoStreamsFound()
    {
        // Arrange
        var query = _fixture.Build<GetStreamsQuery>()
            .With(q => q.PageSize, 10)
            .With(q => q.PageNumber, 1)
            .With(q => q.Search, null as String)
            .With(q => q.CompanyId, null as Guid?)
            .With(q => q.UserId, null as Guid?)
            .Create();
        // Act
        var result = await _handler.Handle(query, _cancellationToken);
        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value);
    }

    [Fact]
    public async Task Handle_ReturnsStreams_WhenSearchIsProvided()
    {
        // Arrange
        var query = _fixture.Build<GetStreamsQuery>()
            .With(q => q.PageSize, 10)
            .With(q => q.PageNumber, 1)
            .With(q => q.Search, "test")
            .With(q => q.CompanyId, null as Guid?)
            .With(q => q.UserId, null as Guid?)
            .Create();

        var streamsList = _fixture.CreateMany<Stream>(5).ToList();
        SetIsDeletedFalse(streamsList);

        foreach (var stream in streamsList)
        {
            typeof(Stream).GetProperty("Name")!.SetValue(stream, "test");
        }


        await _dbContext.Streams.AddRangeAsync(streamsList);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _handler.Handle(query, _cancellationToken);
        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(5, result.Value.TotalRecords);
        Assert.Equal(5, result.Value.Count);
    }

    [Fact]
    public async Task Handle_ReturnsStreamMathingCompanyId_WhenCompanyIdIsProvided()
    {
        // Arrange
        var query = _fixture.Build<GetStreamsQuery>()
            .With(q => q.PageSize, 10)
            .With(q => q.PageNumber, 1)
            .With(q => q.Search, null as String)
            .With(q => q.CompanyId, Guid.NewGuid())
            .With(q => q.UserId, null as Guid?)
            .Create();

        var streamsList = _fixture.CreateMany<Stream>(5).ToList();
        SetIsDeletedFalse(streamsList);

        typeof(Stream).GetProperty("CompanyId")!.SetValue(streamsList[0], query.CompanyId);
        await _dbContext.Streams.AddRangeAsync(streamsList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(1, result.Value.TotalRecords);
        Assert.Single(result.Value);
    }

    private static void SetIsDeletedFalse(List<Stream> streamsList)
    {
        foreach (var stream in streamsList)
        {
            stream.IsDeleted = false;
        }
    }
}

