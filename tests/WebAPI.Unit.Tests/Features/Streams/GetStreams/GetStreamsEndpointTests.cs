using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using MockQueryable;
using Moq;
using WebAPI.Features.Streams.GetStreams;
using WebAPI.Infrastructure.Contracts;
namespace WebAPI.Unit.Tests.Features.Streams.GetStreams;
using Streams = WebAPI.Features.Streams.GetStreams.Streams;

public class GetStreamsEndpointTests
{
    private readonly Mock<ISender> _mockSender;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetStreamsEndpoint _endpoint;

    public GetStreamsEndpointTests()
    {
        _mockSender = new Mock<ISender>();
        _mockMapper = new Mock<IMapper>();
        _endpoint = new GetStreamsEndpoint(_mockSender.Object, _mockMapper.Object);
    }

    [Fact]
    public async Task HandleAsync_ReturnsPaginatedResponse()
    {
        // Arrange
        var request = new GetStreamsRequestFilter(PageNumber: 1, PageSize: 10);
        var cancellationToken = new CancellationToken();

        var testStreams = new List<Streams>
        {
            new() { Id = Guid.NewGuid(), Name = "Stream 1", LastUpdatedUtc = DateTimeOffset.UtcNow },
            new() { Id = Guid.NewGuid(), Name = "Stream 2", LastUpdatedUtc = DateTimeOffset.UtcNow }
        };

        var queryableStreams = testStreams.AsQueryable().BuildMock();

        var paginatedResult = await PaginatedQueryResult<Streams>.CreateAsync(
            queryableStreams, request.PageNumber, request.PageSize, cancellationToken);

        _mockSender
            .Setup(s => s.Send(It.IsAny<GetStreamsQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(paginatedResult);

        _mockMapper
            .Setup(m => m.Map<IEnumerable<Streams>>(It.IsAny<IEnumerable<Streams>>()))
            .Returns([.. testStreams]);

        // Act
        var result = await _endpoint.HandleAsync(request, cancellationToken);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedQueryResult<Streams>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var response = Assert.IsType<PaginatedResponse<Streams>>(okResult.Value);

        response.Items.Should().BeEquivalentTo(testStreams);
        response.PageIndex.Should().Be(request.PageNumber);
        response.PageSize.Should().Be(request.PageSize);
        response.TotalRecords.Should().Be(testStreams.Count);

        _mockSender.Verify(s => s.Send(It.IsAny<GetStreamsQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<IEnumerable<Streams>>(It.IsAny<IEnumerable<Streams>>()), Times.Once);
    }
}
