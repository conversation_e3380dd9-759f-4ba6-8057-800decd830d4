using Apollo.Application.Unit.Tests.Common;
using AutoFixture;
using FluentResults;
using Microsoft.Extensions.Logging;
using Moq;
using SharedKernel.Entities;
using SharedKernel.Services;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.GetTopNews;
using WebAPI.Features.Streams.GetFeedData;
using AiModel = SharedKernel.Entities.AiModel;
using Stream = SharedKernel.Entities.Stream;

namespace WebAPI.Unit.Tests.Features.Streams.GetFeedData;

public class GetFeedDataQueryHandlerTests : HandlerTestBase
{
    private readonly Mock<IColibriApi> _colibriApiMock;
    private readonly Mock<ILogger<GetFeedDataQueryHandler>> _loggerMock;
    private readonly Mock<ICurrentUserService> _currentUserServiceMock;
    private readonly GetFeedDataQueryHandler _handler;

    public GetFeedDataQueryHandlerTests()
    {
        _colibriApiMock = new Mock<IColibriApi>();
        _loggerMock = new Mock<ILogger<GetFeedDataQueryHandler>>();
        _currentUserServiceMock = new Mock<ICurrentUserService>();

        _handler = new GetFeedDataQueryHandler(
            _colibriApiMock.Object,
            _loggerMock.Object,
            _dbContext,
            _currentUserServiceMock.Object);
    }

    [Fact]
    public async Task Handle_WhenUserIdIsNull_ReturnsFailure()
    {
        // Arrange
        _currentUserServiceMock.Setup(x => x.Id).Returns(string.Empty);
        var query = new GetFeedDataQuery();

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.Message.Contains("User ID not found"));
    }

    [Fact]
    public async Task Handle_WhenFeedStreamNotFound_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);
        var query = new GetFeedDataQuery();

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.Message.Contains("Feed stream not found"));
    }

    [Fact]
    public async Task Handle_WhenFeedStreamExists_ReturnsSuccessWithFeedData()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);

        var aiModel = AiModel.Create("Test AI Model");
        _dbContext.AiModels.Add(aiModel);

        var stream = Stream.Create(
            companyId: Guid.NewGuid(),
            aiModelId: aiModel.Id,
            name: "Test Feed Stream",
            prompt: "Test prompt",
            companyName: "Test Company",
            companySpecificStream: true,
            aiGeneratedPrompt: "Generated prompt",
            aiGeneratedPromptId: 123,
            aiModelConfidence: 0.8);

        // Set the stream as a feed stream and assign to user
        stream.GetType().GetProperty("IsFeed")?.SetValue(stream, true);
        stream.CreatedBy = userId;

        _dbContext.Streams.Add(stream);
        await _dbContext.SaveChangesAsync();

        // Mock Colibri API response
        var mockApiResponse = new GetTopNewsResponse
        {
            Result = new List<TopNewsResponse>
            {
                new TopNewsResponse
                {
                    Id = 1,
                    Heading = "Test Article",
                    Url = "https://example.com",
                    Score = 85.0,
                    Typecode = "NEWS",
                    Metadata = new SharedKernel.Services.ColibriApi.GetTopNews.Metadata
                    {
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        People = new List<string> { "John Doe" },
                        Organisations = new List<string> { "Test Org" },
                        Programmes = new List<string> { "Test Programme" }
                    }
                }
            }
        };

        _colibriApiMock
            .Setup(x => x.GetTopNewsAsync(It.IsAny<GetTopNewsRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok(mockApiResponse));

        var query = new GetFeedDataQuery();

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        if (result.IsFailed)
        {
            var errorMessage = string.Join(", ", result.Errors.Select(e => e.Message));
            throw new Exception($"Test failed with errors: {errorMessage}");
        }
        result.IsSuccess.Should().BeTrue();
        var feedData = result.Value.ToList();
        feedData.Should().HaveCount(1);

        var item = feedData.First();
        item.Id.Should().Be(1);
        item.Title.Should().Be("Test Article");
        item.SourceLink.Should().Be("https://example.com");
        item.TypecodeName.Should().Be("NEWS");
        item.People.Should().Contain("John Doe");
        item.Organisations.Should().Contain("Test Org");
        item.Programmes.Should().Contain("Test Programme");
        item.Theme.Should().BeEmpty(); // Theme is not available in Metadata
    }

    [Fact]
    public async Task Handle_WhenColibriApiReturnsFailure_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);

        var aiModel = AiModel.Create("Test AI Model");
        _dbContext.AiModels.Add(aiModel);

        var stream = Stream.Create(
            companyId: Guid.NewGuid(),
            aiModelId: aiModel.Id,
            name: "Test Feed Stream",
            prompt: "Test prompt",
            companyName: "Test Company",
            companySpecificStream: true,
            aiGeneratedPrompt: "Generated prompt",
            aiGeneratedPromptId: 123,
            aiModelConfidence: 0.8);

        // Set the stream as a feed stream and assign to user
        stream.GetType().GetProperty("IsFeed")?.SetValue(stream, true);
        stream.CreatedBy = userId;

        _dbContext.Streams.Add(stream);
        await _dbContext.SaveChangesAsync();

        // Mock Colibri API failure
        _colibriApiMock
            .Setup(x => x.GetTopNewsAsync(It.IsAny<GetTopNewsRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Fail("API Error"));

        var query = new GetFeedDataQuery();

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        var errorMessage = string.Join(", ", result.Errors.Select(e => e.Message));
        Console.WriteLine($"Actual error messages: {errorMessage}");
        result.Errors.Should().ContainSingle(e => e.Message.Contains("API Error"));
    }
}
