using Microsoft.EntityFrameworkCore;
using Moq;
using SharedKernel.Entities;
using SharedKernel.Services;
using WebAPI.Features.Streams.GetFeedItemById;
using WebAPI.Persistence;
using Result = FluentResults.Result;

namespace WebAPI.Unit.Tests.Features.Streams.GetFeedItemById;

public class GetFeedItemByIdQueryHandlerTests : IDisposable
{
    private readonly Mock<IOpenSearchService> _openSearchServiceMock;
    private readonly Mock<ICurrentUserService> _currentUserServiceMock;
    private readonly Mock<IDateTimeService> _dateTimeServiceMock;
    private readonly ApplicationDbContext _dbContext;
    private readonly GetFeedItemByIdQueryHandler _handler;
    private readonly CancellationToken _cancellationToken = CancellationToken.None;

    public GetFeedItemByIdQueryHandlerTests()
    {
        _openSearchServiceMock = new Mock<IOpenSearchService>();
        _currentUserServiceMock = new Mock<ICurrentUserService>();
        _dateTimeServiceMock = new Mock<IDateTimeService>();

        // Create database context with our mock services
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
               .UseInMemoryDatabase(Guid.NewGuid().ToString())
               .Options;

        _dateTimeServiceMock.Setup(x => x.UtcNow).Returns(DateTimeOffset.UtcNow);
        _currentUserServiceMock.Setup(x => x.IdentityId).Returns("00000000-0000-0000-0000-000000000000");

        _dbContext = new ApplicationDbContext(options, _dateTimeServiceMock.Object, _currentUserServiceMock.Object);
        _dbContext.Database.EnsureCreated();

        _handler = new GetFeedItemByIdQueryHandler(
            _openSearchServiceMock.Object,
            _dbContext,
            _currentUserServiceMock.Object);
    }

    public void Dispose()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }

    [Fact]
    public async Task Handle_WhenItemNotFound_ReturnsFailure()
    {
        // Arrange
        var itemId = 123;
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);
        _openSearchServiceMock.Setup(x => x.GetById(itemId, _cancellationToken))
            .ReturnsAsync((ElasticSearchContent)null);

        var query = new GetFeedItemByIdQuery(itemId);

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.Message.Contains($"The feed item with id {itemId} was not found"));
    }

    [Fact]
    public async Task Handle_WhenItemExists_ReturnsSuccessAndTracksRead()
    {
        // Arrange
        var itemId = 123;
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);

        var elasticSearchContent = new ElasticSearchContent(
            PoliticalId: itemId,
            TypeCode: 1,
            TypeCodeName: "NEWS",
            Heading: "Test Article",
            Content: "Test content",
            Url: new Uri("https://example.com"),
            Analysis: "Test analysis"
        );

        _openSearchServiceMock.Setup(x => x.GetById(itemId, _cancellationToken))
            .ReturnsAsync(elasticSearchContent);

        var query = new GetFeedItemByIdQuery(itemId);

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(elasticSearchContent);
    }

    [Fact]
    public async Task Handle_WhenItemAlreadyRead_DoesNotCreateDuplicateReadRecord()
    {
        // Arrange
        var itemId = 123;
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);

        var elasticSearchContent = new ElasticSearchContent(
            PoliticalId: itemId,
            TypeCode: 1,
            TypeCodeName: "NEWS",
            Heading: "Test Article",
            Content: "Test content",
            Url: new Uri("https://example.com"),
            Analysis: "Test analysis"
        );

        _openSearchServiceMock.Setup(x => x.GetById(itemId, _cancellationToken))
            .ReturnsAsync(elasticSearchContent);

        var query = new GetFeedItemByIdQuery(itemId);

        // Act
        var result = await _handler.Handle(query, _cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(elasticSearchContent);
    }

    [Fact]
    public async Task Handle_WhenOpenSearchServiceThrows_PropagatesException()
    {
        // Arrange
        var itemId = 123;
        var userId = Guid.NewGuid().ToString();
        _currentUserServiceMock.Setup(x => x.Id).Returns(userId);
        _openSearchServiceMock.Setup(x => x.GetById(itemId, _cancellationToken))
            .ThrowsAsync(new Exception("OpenSearch error"));

        var query = new GetFeedItemByIdQuery(itemId);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, _cancellationToken));
    }
}
