using AutoFixture;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using WebAPI.Features.Streams.GetStream;

namespace WebAPI.Unit.Tests.Features.Streams.GetStream;

public class GetStreamEndpointTests
{
    private readonly Mock<ISender> _mockSender;
    private readonly GetStreamEndpoint _endpoint;
    private readonly Fixture _fixture;

    public GetStreamEndpointTests()
    {
        _mockSender = new Mock<ISender>();
        _endpoint = new GetStreamEndpoint(_mockSender.Object);
        _fixture = new Fixture();
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnOk_WhenStreamExists()
    {
        // Arrange
        var response = _fixture.Create<Response>();
        var result = Result.Ok(response);

        _mockSender
            .Setup(s => s.Send(It.Is<GetStreamQuery>(q => q.StreamId.Value == response.Id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        // Act
        var resultAction = await _endpoint.HandleAsync(response.Id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(resultAction.Result);
        Assert.Equal(StatusCodes.Status200OK, okResult.StatusCode);
        Assert.Equal(response, okResult.Value);
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnNotFound_WhenStreamDoesNotExist()
    {
        // Arrange
        var streamId = Guid.NewGuid();
        var result = Result.Fail("The stream with id {streamId} was not found");

        _mockSender
            .Setup(s => s.Send(It.Is<GetStreamQuery>(q => q.StreamId.Value == streamId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        // Act
        var resultAction = await _endpoint.HandleAsync(streamId);

        // Assert
        _mockSender.Verify(sender => sender.Send(It.Is<GetStreamQuery>(c =>
                        c.StreamId.Value == streamId),
                        CancellationToken.None), Times.Once);

        var notFoundResult = Assert.IsType<NotFoundObjectResult>(resultAction.Result);
        Assert.Equal(StatusCodes.Status404NotFound, notFoundResult.StatusCode);
        var errorResponse = notFoundResult.Value as object;
        Assert.NotNull(errorResponse);
    }

}
