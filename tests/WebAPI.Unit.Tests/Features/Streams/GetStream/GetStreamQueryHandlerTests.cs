using Apollo.Application.Unit.Tests.Common;
using AutoFixture;
using SharedKernel.Entities;
using WebAPI.Features.Streams.GetStream;
using AiModel = SharedKernel.Entities.AiModel;
using FewShot = SharedKernel.Entities.FewShot;
using Stream = SharedKernel.Entities.Stream;

namespace WebAPI.Unit.Tests.Features.Streams.GetStream;

public class GetStreamQueryHandlerTests : HandlerTestBase
{
    private readonly GetStreamQueryHandler _handler;

    public GetStreamQueryHandlerTests()
    {
        _handler = new GetStreamQueryHandler(_dbContext);
    }

    private void SeedStreamAndRelatedEntities(StreamId streamId)
    {

        var amplifiers = _fixture.CreateMany<string>(3)
            .Select(name => Amplifier.Create(streamId, name, 0.5))
            .ToList();
        var fewShots = _fixture.CreateMany<long>(2)
            .Select(itemId => FewShot.Create(streamId, itemId, 0.8))
            .ToList();
        var streamFilters = _fixture.CreateMany<string>(4)
            .Select(filterValue => StreamMetadataFilter.Create(streamId, StreamMetadataFilterName.People, true, filterValue))
            .ToList();
        var streamFilterTypeCodes = _fixture.CreateMany<int>(2)
            .Select(typeCode => StreamFilterTypeCode.Create(typeCode, streamId, true))
            .ToList();

        _dbContext.Amplifier.AddRange(amplifiers);
        _dbContext.FewShot.AddRange(fewShots);
        _dbContext.StreamMetadataFilter.AddRange(streamFilters);
        _dbContext.StreamFilterTypeCode.AddRange(streamFilterTypeCodes);
    }

    [Fact]
    public async Task Handle_WhenStreamExists_ReturnsStreamWithRelatedData()
    {
        // Arrange
        var aiModel = AiModel.Create("Test AI Model");
        _dbContext.AiModels.Add(aiModel);

        var stream = _fixture.Create<Stream>();
        stream.IsDeleted = false;

        var aiModelIdProperty = typeof(Stream).GetProperty("AiModelId");
        aiModelIdProperty?.SetValue(stream, aiModel.Id);

        _dbContext.Streams.Add(stream);
        SeedStreamAndRelatedEntities(stream.Id);
        await _dbContext.SaveChangesAsync();

        var query = new GetStreamQuery(stream.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var response = result.Value;

        response.Id.Should().Be(stream.Id.Value);
        response.Name.Should().NotBeNullOrEmpty();
        response.Filters.Should().NotBeNull();
        response.ExclusionFilters.Should().NotBeNull();
        response.AiParameters.Should().NotBeNull();
        response.AdvancedSettings.Should().NotBeNull();
        response.Company.Should().NotBeNull();
        response.Company.Name.Should().NotBeNullOrEmpty();

        // Validate counts for related entities
        var amplifiersCount = _dbContext.Amplifier.Count(a => a.StreamId == stream.Id);
        var fewShotsCount = _dbContext.FewShot.Count(fs => fs.StreamId == stream.Id);
        var streamFiltersCount = _dbContext.StreamMetadataFilter.Count(sf => sf.StreamId == stream.Id);
        var streamFilterTypeCodesCount = _dbContext.StreamFilterTypeCode.Count(sfc => sfc.StreamId == stream.Id);

        amplifiersCount.Should().Be(response.AdvancedSettings.Amplifiers.Count());
        fewShotsCount.Should().Be(response.AdvancedSettings.FewShots.Count());
    }

    [Fact]
    public async Task Handle_WhenStreamDoesNotExist_ReturnsFailure()
    {
        // Arrange
        var nonExistentStreamId = new StreamId(Guid.NewGuid());
        var query = new GetStreamQuery(nonExistentStreamId);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.IsFailed.Should().BeTrue();
        result.Errors.Should()
            .ContainSingle(e => e.Message.Contains($"The stream with id {nonExistentStreamId} was not found"));
    }
}
