using AutoFixture;
using FluentValidation.TestHelper;
using SharedKernel.Entities;
using WebAPI.Features.Streams.GetStream;

namespace WebAPI.Unit.Tests.Features.Streams.GetStream;
public class GetStreamQueryValidatorTests
{
    private readonly GetStreamQueryValidator _validator;
    private readonly Fixture _fixture;
    public GetStreamQueryValidatorTests()
    {
        _validator = new GetStreamQueryValidator();
        _fixture = new Fixture();
    }

    [Fact]
    public void GetStreamQueryValidator_WhenStreamIdIsEmpty_ReturnsFailure()
    {
        // Arrange
        var query = _fixture.Build<GetStreamQuery>()
                            .With(q => q.StreamId, new StreamId(Guid.Empty))
                            .Create();
        // Act
        var result = _validator.TestValidate(query);
        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(q => q.StreamId);
    }

    [Fact]
    public void GetStreamQueryValidator_WhenStreamIdIsNotEmpty_ReturnsSuccess()
    {
        // Arrange
        var query = _fixture.Create<GetStreamQuery>();
        // Act
        var result = _validator.TestValidate(query);
        // Assert
        result.IsValid.Should().BeTrue();
    }
}
