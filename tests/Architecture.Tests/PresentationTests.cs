using Architecture.Tests.Fixtures;
using FluentValidation;
using NetArchTest.Rules;
using SharedKernel.Primitives.Interfaces;

namespace Architecture.Tests;

public class ApplicationTests : BaseTest
{
    [Fact]
    public void AbstractValidator_Should_BeSealed()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .Inherit(typeof(AbstractValidator<>))
            .Should()
            .BeSealed()
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void AbstractValidator_Should_HaveNameEndingWithValidator()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .Inherit(typeof(AbstractValidator<>))
            .Should()
            .HaveNameEndingWith("Validator")
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void IQuery_Should_HaveNameEndingWithQuery()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(IQuery<>))
            .Should()
            .HaveNameEndingWith("Query")
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void IQuery_Should_BeSealed()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(IQuery<>))
            .Should()
            .BeSealed()
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void IQueryHandler_Should_HaveNameEndingWithQueryHandler()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(IQueryHandler<,>))
            .Should()
            .HaveNameEndingWith("QueryHandler")
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void IQueryHandler_Should_BeSealed()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(IQueryHandler<,>))
            .Should()
            .BeSealed()
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void IQueryHandler_Should_NotBePublic()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(IQueryHandler<,>))
            .Should()
            .NotBePublic()
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void ICommand_Should_BeSealed()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(ICommand)).Or().ImplementInterface(typeof(ICommand<>))
            .Should()
            .BeSealed()
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void ICommand_Should_HaveNameEndingWithCommand()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(ICommand)).Or().ImplementInterface(typeof(ICommand<>))
            .Should()
            .HaveNameEndingWith("Command")
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void ICommandHandler_Should_HaveNameEndingWithCommandHandler()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(ICommandHandler<>)).Or().ImplementInterface(typeof(ICommandHandler<,>))
            .Should()
            .HaveNameEndingWith("CommandHandler")
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void ICommandHandler_Should_BeSealed()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(ICommandHandler<>)).Or().ImplementInterface(typeof(ICommandHandler<,>))
            .Should()
            .BeSealed()
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void ICommandHandler_Should_NotBePublic()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ImplementInterface(typeof(ICommandHandler<>)).Or().ImplementInterface(typeof(ICommandHandler<,>))
            .Should()
            .NotBePublic()
            .GetResult();

        AssertResult(result);
    }

    [Fact]
    public void Configurations_Should_BeSealed()
    {
        var result = Types.InAssembly(PresentationAssembly)
            .That()
            .ResideInNamespace("WebAPI.Infrastructure.Configurations")
            .Should()
            .BeSealed()
            .GetResult();

        AssertResult(result);
    }
}


