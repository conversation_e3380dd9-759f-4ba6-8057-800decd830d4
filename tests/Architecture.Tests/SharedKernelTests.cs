using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Architecture.Tests.Fixtures;
using NetArchTest.Rules;
using SharedKernel.DataAudit;
using SharedKernel.Primitives.Abstract;
using SharedKernel.Primitives.Interfaces;

namespace Architecture.Tests;

public class SharedKernelTests : BaseTest
{
    [Fact]
    public void BaseEntity_Should_Have_Protected_Parameterless_Constructor()
    {
        var entityTypes = Types.InAssembly(SharedKernelAssembly)
            .That()
            .Inherit(typeof(BaseEntity))
            .GetTypes();
        var failingTypes = new List<Type>();

        foreach (var entityType in entityTypes)
        {
            var constructors = entityType.GetConstructors(
                BindingFlags.NonPublic |
                BindingFlags.Instance);
            if (!constructors.Any(c => c.IsFamily && c.GetParameters().Length == 0))
            {
                failingTypes.Add(entityType);
            }
        }

        failingTypes.Should().BeEmpty();
    }

    [Fact]
    public void BaseEntity_Should_Have_StaticCreateMethod()
    {
        var domainEntityTypes = Types.InAssembly(SharedKernelAssembly)
            .That()
            .Inherit(typeof(BaseEntity))
            .GetTypes();

        foreach (var entityType in domainEntityTypes)
        {
            var createMethod = entityType.GetMethod("Create",
                BindingFlags.Static | BindingFlags.Public);

            createMethod.Should().NotBeNull();
            createMethod!.IsStatic.Should().BeTrue($"{entityType.Name} does not have a static Create method.");
        }
    }

    [Fact]
    public void BaseEntity_Should_OnlyHave_PrivateSetter()
    {
        var entityTypes = Types.InAssembly(SharedKernelAssembly)
            .That()
            .AreClasses()
            .And()
            .Inherit(typeof(BaseEntity))
            .GetTypes();

        foreach (var entityType in entityTypes)
        {
            var auditableEntityType = typeof(AuditableEntity)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .ToArray();

            var softDeleteEntityType = typeof(ISoftDeletable)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .ToArray();

            var excludeType = auditableEntityType
                .Concat(softDeleteEntityType)
                .Select(x => x.Name)
                .ToArray();

            var properties = entityType
                .GetProperties()
                .Where(p => !excludeType.Contains(p.Name));

            foreach (var property in properties)
            {
                if (property.CanWrite)
                {
                    property.SetMethod.Should().NotBeNull()
                        .And.Match(setMethod => setMethod.IsPrivate || setMethod.IsFamily || setMethod.IsFamilyOrAssembly,
                            $"{entityType.Name}.{property.Name} should have a private or protected setter.", property.DeclaringType?.FullName);
                }
            }
        }
    }

}
