<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="NetArchTest.Rules" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\SharedKernel\SharedKernel.csproj" />
      <ProjectReference Include="..\..\src\WebAPI\WebAPI.csproj" />
    </ItemGroup>

</Project>

