using System.Reflection;
using NetArchTest.Rules;
using SharedKernel;

namespace Architecture.Tests.Fixtures;

public abstract class BaseTest
{
    protected static readonly Assembly SharedKernelAssembly = AssemblyReference.Assembly;
    protected static readonly Assembly PresentationAssembly = typeof(Program).Assembly;

    protected static void AssertResult(TestResult result)
    {
        result.FailingTypes.Should().BeNullOrEmpty();
        result.IsSuccessful.Should().BeTrue();
    }
}
