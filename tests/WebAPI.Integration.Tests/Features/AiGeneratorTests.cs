using System.Threading.Tasks;
using WebAPI.Features.AiGenerator.GenerateAiPrompt;
using WebAPI.Integration.Tests.Fixtures;

namespace WebAPI.Integration.Tests.Features;

public class AiGeneratorTests : BaseIntegrationTest
{
    public AiGeneratorTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task GenerateAiPrompt_WhenGenerateAiPromptCommandIsInvalid_ShouldReturnAFailureResult()
    {
        // Arrange
        var command = new GenerateAiPromptQuery(null);

        // Act
        var commandResult = await Sender.Send(command);

        // Assert
        commandResult.IsSuccess.Should().BeFalse();
    }
}
