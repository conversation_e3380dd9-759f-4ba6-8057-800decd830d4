using System.Linq;
using System.Threading.Tasks;
using WebAPI.Features.TypeCodes.GetTypeCode;
using WebAPI.Features.TypeCodes.GetTypeCodes;
using WebAPI.Features.TypeCodes.GetTypeCodesById;
using WebAPI.Integration.Tests.Fixtures;

namespace WebAPI.Integration.Tests.Features;

public class TypeCodeTests : BaseIntegrationTest
{
    public TypeCodeTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task GetTypeCodes_WhenSearchKeywordIsProvided_ShouldFilterResults()
    {
        // Arrange
        var typeCodes = CreateTypeCodes(10);
        var searchKeyword = typeCodes[0].Name[..3];

        var query = new GetTypeCodesQuery(searchKeyword, null);

        // Act
        var queryResult = await Sender.Send(query);

        // Assert
        queryResult.IsSuccess.Should().BeTrue();
        queryResult.Value.Should().NotBeEmpty();
        queryResult.Value.Should()
            .OnlyContain(typeCode => typeCode.Name.Contains(searchKeyword));
    }

    [Fact]
    public async Task GetTypeCodes_WhenIdsAreProvided_ShouldReturnTypeCodes()
    {
        // Arrange
        var typeCodes = CreateTypeCodes(10);
        var ids = typeCodes.Select(typeCode => typeCode.Id).ToList();

        var query = new GetTypeCodesByIdsQuery(ids);

        // Act
        var queryResult = await Sender.Send(query);

        // Assert
        queryResult.IsSuccess.Should().BeTrue();
        queryResult.Value.Should().NotBeEmpty();
        queryResult.Value.Should().HaveCount(ids.Count);
    }

    [Fact]
    public async Task GetTypeCode_WhenTypeCodeExists_ShouldReturnTypeCode()
    {
        // Arrange
        var typeCodes = CreateTypeCodes(10);
        var typeCode = typeCodes.First();

        var query = new GetTypeCodeQuery(typeCode.Id);

        // Act
        var queryResult = await Sender.Send(query);

        // Assert
        queryResult.IsSuccess.Should().BeTrue();
        queryResult.Value.Should().NotBeNull();
        queryResult.Value.Id.Should().Be(typeCode.Id);
    }

    [Fact]
    public async Task GetTypeCode_WhenTypeCodeDoesNotExist_ShouldReturnNotFound()
    {
        // Arrange
        CreateTypeCodes(10);

        var query = new GetTypeCodeQuery(10000);

        // Act
        var queryResult = await Sender.Send(query);

        // Assert
        queryResult.IsSuccess.Should().BeFalse();
        queryResult.Errors.Should().NotBeNull();
        queryResult.Errors.Should().HaveCount(1);
        queryResult.Errors.First().Message.Should().StartWith("The type code with id 10000 was not found");
        queryResult.Errors.First().Reasons.First().Message.Should().Be("Type Code Id Not Found");
    }

    [Fact]
    public async Task GetTypeCode_WhenTypeCodeIdIsInvalid_ShouldReturnValidationError()
    {
        // Arrange
        CreateTypeCodes(10);

        var query = new GetTypeCodeQuery(-213);

        // Act
        var queryResult = await Sender.Send(query);

        // Assert
        queryResult.IsSuccess.Should().BeFalse();
        queryResult.Errors.Should().NotBeNull();
        queryResult.Errors.Should().HaveCount(1);
        queryResult.Errors.First().Message.Should().Be("'Id' must be greater than '0'.");
        queryResult.Errors.First().Reasons.First().Message.Should().Be("Id");
    }
}
