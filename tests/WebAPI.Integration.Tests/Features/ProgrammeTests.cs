using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SharedKernel.Entities;
using WebAPI.Features.Programmes.GetProgrammes;
using WebAPI.Infrastructure.Enums;
using WebAPI.Integration.Tests.Fixtures;

namespace WebAPI.Integration.Tests.Features;

public class ProgrammeTests : BaseIntegrationTest
{
    public ProgrammeTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    { }

    [Theory]
    [InlineData(ProgrammeSortProperty.Name, PropertySortType.ASC)]
    [InlineData(ProgrammeSortProperty.Name, PropertySortType.DESC)]
    public async Task GetProgrammes_WhenSortingBySpecificProperty_ShouldReturnSortedByThatProperty(ProgrammeSortProperty sortProperty, PropertySortType sortType)
    {
        // Arrange
        CreatePeoples(15);
        var query = new GetProgrammesQuery(string.Empty, 1, 10, SortProperty: sortProperty, SortType: sortType);

        // Act
        var queryResult = await Sender.Send(query);

        // Assert
        AssertItemsAreSortedByProperty(queryResult.Value.Items, sortProperty, sortType);
    }

    private static void AssertItemsAreSortedByProperty<T>(IEnumerable<T> items, ProgrammeSortProperty sortProperty, PropertySortType sortType)
    {
        Func<T, string> propertySelector = sortProperty switch
        {
            _ => item => ((GetProgrammesResponse)(object)item).Name
        };

        var comparison = (Func<T, T, int>)((x, y) =>
            string.Compare(propertySelector(x), propertySelector(y), StringComparison.CurrentCultureIgnoreCase));

        if (sortType == PropertySortType.ASC)
        {
            items.Should().BeInAscendingOrder(comparison);
        }
        else
        {
            items.Should().BeInDescendingOrder(comparison);
        }
    }

}
