using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SharedKernel.Entities;
using WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;
using WebAPI.Features.Streams.CreateStream;
using WebAPI.Features.Streams.Shared;
using WebAPI.Integration.Tests.Fixtures;

namespace WebAPI.Integration.Tests.Features;

public class StreamAlertTests : BaseIntegrationTest
{
    public StreamAlertTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
    }

    private async Task<StreamAlert> CreateStreamAlertAsync(Guid streamId)
    {
        var streamAlert = StreamAlert.Create(
            new StreamId(streamId),
            StreamAlertFrequency.Day,
            StreamAlertRecurrence.Repeated,
            DateTimeOffset.UtcNow.AddDays(-1),
            DateTimeOffset.UtcNow.AddDays(1),
            null,
            "Test",
            [
                "email1",
                "email2"
            ],
            null
        );

        DbContext.StreamAlerts.Add(streamAlert);
        await DbContext.SaveChangesAsync();

        return streamAlert;
    }

    private static CreateStreamCommand CreateValidStreamCommand(List<Guid> aiModels)
    {
        var request = new CreateRequestResponse(
            "default(string)",
            null,
            "Test Prompt",
            "Ai Test",
            new Filters(new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<int>()),
            new ExclusionFilters(new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<int>()),
            new AiParameters(aiModels, .9D),
            new AdvancedSettings(new List<string>(), 0D, new List<long?>(), 0D)
        );

        return new CreateStreamCommand(request);
    }

    [Fact]
    public async Task UpdateStreamAlertLastSent_When_CommandIsValid_Should_UpdateLastSentAndNextAlertDate()
    {
        // Arrange
        var aiModel = CreateAiModel().Select(a => a.Id.Value).ToList();

        var createStreamCommand = CreateValidStreamCommand(aiModel);

        var createStreamCommandResult = await Sender.Send(createStreamCommand);

        Guid streamId = Guid.Empty;

        if (createStreamCommandResult.Value.Id != null)
        {
            streamId = createStreamCommandResult.Value.Id.Value;
        }

        var streamAlert = await CreateStreamAlertAsync(streamId);

        var command = new UpdateStreamAlertLastSentCommand(streamAlert.Id.Value, DateTimeOffset.UtcNow.AddHours(-1));

        // Act
        var commandResult = await Sender.Send(command);

        // Assert
        commandResult.IsSuccess.Should().BeTrue();
        commandResult.Value.Should().NotBeNull();

        var updatedStreamAlert = DbContext.StreamAlerts.FirstOrDefault(x => x.Id == commandResult.Value.Id);
        updatedStreamAlert.Should().NotBeNull();
        updatedStreamAlert!.LastSent.Should().Be(command.LastSent);
        updatedStreamAlert.NextAlertDate.Should().BeAfter(command.LastSent);
    }

    [Fact]
    public async Task UpdateStreamAlertLastSent_When_StreamAlertNotFound_Should_ReturnFailure()
    {
        // Arrange
        var streamAlertId = Guid.NewGuid();
        var command = new UpdateStreamAlertLastSentCommand(streamAlertId, DateTimeOffset.UtcNow);

        // Act
        var commandResult = await Sender.Send(command);

        // Assert
        commandResult.IsSuccess.Should().BeFalse();
        commandResult.Errors.Should()
            .ContainSingle(e => e.Message == $"Stream alert with ID {streamAlertId} was not found");
    }
}
