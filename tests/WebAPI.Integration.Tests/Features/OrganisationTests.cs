using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Bogus;
using SharedKernel.Entities;
using WebAPI.Features.Organisations.GetOrganisations;
using WebAPI.Infrastructure.Enums;
using WebAPI.Integration.Tests.Fixtures;

namespace WebAPI.Integration.Tests.Features;

public class OrganisationTests : BaseIntegrationTest
{
    public OrganisationTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
    }


    [Theory]
    [InlineData(OrganisationSortProperty.Name, PropertySortType.ASC)]
    [InlineData(OrganisationSortProperty.Name, PropertySortType.DESC)]
    public async Task GetOrganisation_WhenSortingBySpecificProperty_ShouldReturnSortedByThatProperty(OrganisationSortProperty sortProperty, PropertySortType sortType)
    {
        // Arrange
        CreatePeoples(15);
        var query = new GetOrganisationsQuery(string.Empty, 1, 10, SortProperty: sortProperty, SortType: sortType);

        // Act
        var queryResult = await Sender.Send(query);

        // Assert
        AssertItemsAreSortedByProperty(queryResult.Value.Items, sortProperty, sortType);
    }

    private static void AssertItemsAreSortedByProperty<T>(IEnumerable<T> items, OrganisationSortProperty sortProperty, PropertySortType sortType)
    {
        Func<T, string> propertySelector = sortProperty switch
        {
            _ => item => ((GetOrganisationResponse)(object)item).Name
        };

        var comparison = (Func<T, T, int>)((x, y) =>
            string.Compare(propertySelector(x), propertySelector(y), StringComparison.CurrentCultureIgnoreCase));

        if (sortType == PropertySortType.ASC)
        {
            items.Should().BeInAscendingOrder(comparison);
        }
        else
        {
            items.Should().BeInDescendingOrder(comparison);
        }
    }
}
