using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebAPI.Features.Peoples.GetPeople;
using WebAPI.Infrastructure.Enums;
using WebAPI.Integration.Tests.Fixtures;

namespace WebAPI.Integration.Tests.Features;

public class PeopleTests : BaseIntegrationTest
{
    public PeopleTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    { }

    [Theory]
    [InlineData(PeopleSortProperty.Name, PropertySortType.ASC)]
    [InlineData(PeopleSortProperty.Name, PropertySortType.DESC)]
    public async Task GetPeoples_WhenSortingBySpecificProperty_ShouldReturnSortedByThatProperty(PeopleSortProperty sortProperty, PropertySortType sortType)
    {
        // Arrange
        CreatePeoples(15);
        var query = new GetPeopleQuery(string.Empty, 1, 10, SortProperty: sortProperty, SortType: sortType);

        // Act
        var queryResult = await Sender.Send(query);

        // Assert
        AssertItemsAreSortedByProperty(queryResult.Value.Items, sortProperty, sortType);
    }

    private static void AssertItemsAreSortedByProperty<T>(IEnumerable<T> items, PeopleSortProperty sortProperty, PropertySortType sortType)
    {
        Func<T, string> propertySelector = sortProperty switch
        {
            _ => item => ((GetPeopleResponse)(object)item).Name
        };

        var comparison = (Func<T, T, int>)((x, y) =>
            string.Compare(propertySelector(x), propertySelector(y), StringComparison.CurrentCultureIgnoreCase));

        if (sortType == PropertySortType.ASC)
        {
            items.Should().BeInAscendingOrder(comparison);
        }
        else
        {
            items.Should().BeInDescendingOrder(comparison);
        }
    }
}
