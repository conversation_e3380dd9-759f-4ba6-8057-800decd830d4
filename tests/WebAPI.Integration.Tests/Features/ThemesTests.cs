using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebAPI.Features.Peoples.GetPeople;
using WebAPI.Features.Themes.GetThemes;
using WebAPI.Infrastructure.Enums;
using WebAPI.Integration.Tests.Fixtures;

namespace WebAPI.Integration.Tests.Features;

public class ThemesTests : BaseIntegrationTest
{
    public ThemesTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    { }

    [Theory]
    [InlineData(ThemesSortProperty.Name, PropertySortType.ASC)]
    [InlineData(ThemesSortProperty.Name, PropertySortType.DESC)]
    public async Task GetPeoples_WhenSortingBySpecificProperty_ShouldReturnSortedByThatProperty(ThemesSortProperty sortProperty, PropertySortType sortType)
    {
        // Arrange
        CreateThemes(15);
        var query = new GetThemesQuery(string.Empty, 1, 10, SortProperty: sortProperty, SortType: sortType);

        // Act
        var queryResult = await Sender.Send(query);

        // Assert
        AssertItemsAreSortedByProperty(queryResult.Value.Items, sortProperty, sortType);
    }

    private static void AssertItemsAreSortedByProperty<T>(IEnumerable<T> items, ThemesSortProperty sortProperty, PropertySortType sortType)
    {
        Func<T, string> propertySelector = sortProperty switch
        {
            _ => item => ((GetThemesResponse)(object)item).Name
        };

        var comparison = (Func<T, T, int>)((x, y) =>
            string.Compare(propertySelector(x), propertySelector(y), StringComparison.CurrentCultureIgnoreCase));

        if (sortType == PropertySortType.ASC)
        {
            items.Should().BeInAscendingOrder(comparison);
        }
        else
        {
            items.Should().BeInDescendingOrder(comparison);
        }
    }
}
