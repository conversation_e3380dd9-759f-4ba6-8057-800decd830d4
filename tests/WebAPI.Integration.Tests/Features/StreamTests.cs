using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoFixture;
using SharedKernel.Entities;
using WebAPI.Features.Streams.CreateStream;
using WebAPI.Features.Streams.Shared;
using WebAPI.Integration.Tests.Fixtures;

namespace WebAPI.Integration.Tests.Features;

public class StreamTests : BaseIntegrationTest
{
    private readonly Fixture _fixture;

    public StreamTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
        _fixture = new Fixture();
    }

    private static CreateStreamCommand CreateInvalidStreamCommand()
    {
        var request = new CreateRequestResponse(
            default(string),
            null,
            "Test Prompt",
            "",
            new Filters(new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<int>()),
            new ExclusionFilters(new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<int>()),
            new AiParameters(new List<Guid> { Guid.Empty }, 0D),
            new AdvancedSettings(new List<string>(), 0D, new List<long?>(), 0D)
        );

        return new CreateStreamCommand(request);
    }

    private static CreateStreamCommand CreateValidStreamCommand(List<Guid> aiModels)
    {
        var request = new CreateRequestResponse(
            "default(string)",
            null,
            "Test Prompt",
            "Ai Test",
            new Filters(new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<int>()),
            new ExclusionFilters(new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<int>()),
            new AiParameters(aiModels, .9D),
            new AdvancedSettings(new List<string>(), 0D, new List<long?>(), 0D)
        );

        return new CreateStreamCommand(request);
    }

    private static CreateStreamCommand CreateValidMentionStreamCommand()
    {
        var request = new CreateRequestResponse(
            "Mention Stream Test",
            null,
            null,
            null,
            new Filters(new List<Filter> { new(Guid.NewGuid(), "Boris Johnson") }, new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<int>()),
            new ExclusionFilters(new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<Filter>(), new List<int>()),
            null,
            null,
            0,
            true
        );

        return new CreateStreamCommand(request);
    }


    [Fact]
    public async Task CreateStream_When_CreateStreamCommandIsInvalid_Should_ReturnAFailureResult()
    {
        // Arrange
        var command = CreateInvalidStreamCommand();

        // Act
        var commandResult = await Sender.Send(command);

        // Assert
        commandResult.IsSuccess.Should().BeFalse();
        commandResult.Errors.Should().NotBeEmpty();
    }

    [Fact]
    public async Task CreateStream_When_CreateStreamCommandIsValid_Should_ReturnASuccessResult_And_CreateStream()
    {
        // Arrange
        var aiModel = CreateAiModel().Select(a => a.Id.Value).ToList();

        var command = CreateValidStreamCommand(aiModel);

        // Act
        var commandResult = await Sender.Send(command);

        // Assert
        commandResult.IsSuccess.Should().BeTrue();
        commandResult.Value.Should().NotBeNull();

        var streamId = new StreamId(commandResult.Value.Id.Value);

        var createdStream = DbContext.Streams.FirstOrDefault(x => x.Id == streamId);
        createdStream.Should().NotBeNull();
        createdStream!.Name.Should().Be(command.Request.Name);
    }

    [Fact]
    public async Task
        CreateStream_When_CreateStreamCommandIsValidMentionStream_Should_ReturnASuccessResult_And_CreateMentionStream()
    {
        // Arrange
        var command = CreateValidMentionStreamCommand();

        // Act
        var commandResult = await Sender.Send(command);

        // Assert
        commandResult.IsSuccess.Should().BeTrue();
        commandResult.Value.Should().NotBeNull();

        var streamId = new StreamId(commandResult.Value.Id.Value);

        var createdStream = DbContext.Streams.FirstOrDefault(x => x.Id == streamId);
        createdStream.Should().NotBeNull();
        createdStream!.Name.Should().Be(command.Request.Name);
    }
}
