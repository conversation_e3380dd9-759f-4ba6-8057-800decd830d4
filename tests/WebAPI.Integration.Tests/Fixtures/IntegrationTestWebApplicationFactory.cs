using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services.ColibriApi;
using Testcontainers.MySql;
using WebAPI.Persistence;

namespace WebAPI.Integration.Tests.Fixtures;

public class IntegrationTestWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private readonly MySqlContainer _dbContainer = new MySqlBuilder().WithDatabase("StreamsDb").WithUsername("SteamsUser").Build();

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureTestServices(services =>
        {
            services.RemoveAll<DbContextOptions<ApplicationDbContext>>();

            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseMySql(_dbContainer.GetConnectionString(),
                    new MySqlServerVersion(new Version(8, 0, 21))));

            services.RemoveAll<IColibriApi>();
            services.AddScoped<IColibriApi, MockApiClient>();

            services.RemoveAll<IDistributedCache>();
            services.RemoveAll<ICacheService>();
            services.AddScoped<ICacheService, MockDistributedCache>();
        });
    }

    public async Task InitializeAsync()
    {
        await _dbContainer.StartAsync();
    }

    public new async Task DisposeAsync()
    {
        await _dbContainer.StopAsync();
    }
}
