using System.Threading;
using System.Threading.Tasks;
using AutoFixture;
using FluentResults;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.AddAmplifiers;
using SharedKernel.Services.ColibriApi.AddFewShots;
using SharedKernel.Services.ColibriApi.ExpandClientProfile;
using SharedKernel.Services.ColibriApi.GetArticleRationale;
using SharedKernel.Services.ColibriApi.GetTopNews;
using SharedKernel.Services.ColibriApi.UniqueAiMetadata;

namespace WebAPI.Integration.Tests.Fixtures;

public class MockApiClient : IColibriApi
{
    private readonly Fixture _fixture = new();

    public Task<Result<AddAmplifiersResponse>> AddAmplifiers(AddAmplifiersRequest request, CancellationToken cancellationToken)
    {
        var response = _fixture.Build<AddAmplifiersResponse>().Without(p => p.ErrorAmplifiers).Create();

        return Task.FromResult(Result.Ok(response));
    }

    public Task<Result<AddFewShotsResponse>> AddFewShots(AddFewShotsRequest request, CancellationToken cancellationToken)
    {
        var response = _fixture.Build<AddFewShotsResponse>().Without(p => p.ErrorFewShots).Create();

        return Task.FromResult(Result.Ok(response));
    }

    public Task<Result<ExpandClientProfileResponse>> ExpandClientProfileAsync(ExpandClientProfileRequest request, CancellationToken cancellationToken)
    {
        return Task.FromResult(Result.Ok(_fixture.Create<ExpandClientProfileResponse>()));
    }

    public Task<Result<GetArticleRationaleResponse>> GetArticleRationaleAsync(GetArticleRationaleRequest request, CancellationToken cancellationToken)
    {
        return Task.FromResult(Result.Ok(_fixture.Create<GetArticleRationaleResponse>()));
    }

    public Task<Result<GetTopNewsResponse>> GetTopNewsAsync(GetTopNewsRequest request, CancellationToken cancellationToken)
    {
        return Task.FromResult(Result.Ok(_fixture.Create<GetTopNewsResponse>()));
    }

    public Task<Result<UniqueAiMetadataResponse>> GetUniqueAiMetadataAsync(CancellationToken cancellationToken)
    {
        return Task.FromResult(Result.Ok(_fixture.Create<UniqueAiMetadataResponse>()));
    }
}
