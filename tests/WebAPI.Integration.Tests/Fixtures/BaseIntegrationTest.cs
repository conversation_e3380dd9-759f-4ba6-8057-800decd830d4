using System.Collections.Generic;
using Bogus;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using SharedKernel.Entities;
using WebAPI.Persistence;

namespace WebAPI.Integration.Tests.Fixtures;

public class BaseIntegrationTest : IClassFixture<IntegrationTestWebApplicationFactory>
{
    protected readonly ISender Sender;
    protected readonly ApplicationDbContext DbContext;

    protected BaseIntegrationTest(IntegrationTestWebApplicationFactory factory)
    {
        IServiceScope serviceScope = factory.Services.CreateScope();

        Sender = serviceScope.ServiceProvider.GetRequiredService<ISender>();
        DbContext = serviceScope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    }

    protected List<AiModel> CreateAiModel(int totalOfItems = 5)
    {
        var aiModels = new Faker<AiModel>().CustomInstantiator(f => AiModel.Create(
            f.Name.FirstName())).Generate(totalOfItems);

        DbContext.AiModels.AddRange(aiModels);
        DbContext.SaveChanges();

        return aiModels;
    }

    protected List<People> CreatePeoples(int totalOfItems = 5)
    {
        var people = new Faker<People>().CustomInstantiator(f => People.Create(
            new FirstName(f.Person.FirstName),
            new Surname(f.Person.LastName))).Generate(totalOfItems);

        DbContext.Peoples.AddRange(people);
        DbContext.SaveChanges();

        return people;
    }

    protected List<TypeCode> CreateTypeCodes(int totalOfItems = 5)
    {
        var typeCodes = new Faker<TypeCode>()
            .CustomInstantiator(f => TypeCode.Create(f.Company.CompanyName(), 999))
            .Generate(totalOfItems);

        DbContext.TypeCodes.AddRange(typeCodes);
        DbContext.SaveChanges();

        return typeCodes;
    }

    protected void CreateThemes(int totalOfItems = 5)
    {
        var themeTags = new Faker<ThemeTag>().Generate(totalOfItems);

        DbContext.ThemeTags.AddRange(themeTags);
        DbContext.SaveChanges();
    }
}
