using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SharedKernel.Primitives.Interfaces;

namespace WebAPI.Integration.Tests.Fixtures;

public class MockDistributedCache : ICacheService
{
    public Task<T> GetAsync<T>(string key)
    {
        if (typeof(T) == typeof(ICollection<string>))
        {
            var fakeData = new List<string> { "Data1", "Data2", "Data3" };
            return Task.FromResult((T)(object)fakeData);
        }

        return Task.FromResult(default(T));
    }

    public Task SetAsync<T>(string key, T value, TimeSpan? absoluteExpirationRelativeToNow = null)
    {
        return Task.CompletedTask;
    }

    public Task RemoveAsync(string key)
    {
        return Task.CompletedTask;
    }
}
