using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Integration.Tests.Fixtures;
using TypeCode = SharedKernel.Entities.TypeCode;

namespace WebAPI.Integration.Tests.Infrastructure.Extensions;

public class ApplicationDbContextExtensionsTests : BaseIntegrationTest
{
    public ApplicationDbContextExtensionsTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
    }

    private async Task CreateTypeCodeStructure()
    {
        var cancellationToken = CancellationToken.None;
        var entities = await DbContext.TypeCodes.ToListAsync(cancellationToken: cancellationToken);
        DbContext.TypeCodes.RemoveRange(entities);

        var houseOfCommons = TypeCode.Create("House Of Commons", 999);
        SetPrivateProperty(houseOfCommons, "Id", 61);
        SetPrivateProperty(houseOfCommons, "ParentId", 999);

        var houseOfLords = TypeCode.Create("House Of Lords", 999);
        SetPrivateProperty(houseOfLords, "Id", 63);
        SetPrivateProperty(houseOfLords, "ParentId", 999);

        var houseOfCommonsOralQuestion = TypeCode.Create("House of Commons - Oral Question Tabled", 999);
        SetPrivateProperty(houseOfCommonsOralQuestion, "Id", 92);
        SetPrivateProperty(houseOfCommonsOralQuestion, "ParentId", 61);

        var houseOfCommonsWrittenQuestion = TypeCode.Create("House of Commons - Written Question Tabled", 999);
        SetPrivateProperty(houseOfCommonsWrittenQuestion, "Id", 93);
        SetPrivateProperty(houseOfCommonsWrittenQuestion, "ParentId", 61);

        var commonsSelectCommitteePressRelease = TypeCode.Create("Commons Select Committee Press Release", 999);
        SetPrivateProperty(commonsSelectCommitteePressRelease, "Id", 101080);
        SetPrivateProperty(commonsSelectCommitteePressRelease, "ParentId", 438057464);

        var data = new List<TypeCode>
        {
            houseOfCommons,
            houseOfLords,
            houseOfCommonsOralQuestion,
            houseOfCommonsWrittenQuestion,
            commonsSelectCommitteePressRelease
        };

        await DbContext.AddRangeAsync(data, cancellationToken);
        await DbContext.SaveChangesAsync(cancellationToken);

        var results = await DbContext.TypeCodes.ToListAsync(cancellationToken);
    }

    [Fact]
    public async Task GetInflatedTypeCodesAsync_ReturnsCorrectTypeCodeChildrenAndNonParentCodes()
    {
        // Arrange
        await CreateTypeCodeStructure();

        // Act
        var result = await DbContext.GetInflatedTypeCodesAsync([61, 101080], CancellationToken.None);

        // Assert

        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(3);
        result.Should().BeEquivalentTo([92, 93, 101080]);
    }

    private static void SetPrivateProperty<T>(T obj, string propertyName, object value)
    {
        var property = typeof(T).GetProperty(propertyName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
        if (property == null)
        {
            throw new ArgumentException($"Property {propertyName} not found.");
        }

        property.SetValue(obj, value);
    }
}
