<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="Testcontainers.MySql" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\WebAPI\WebAPI.csproj" />
    </ItemGroup>

</Project>

