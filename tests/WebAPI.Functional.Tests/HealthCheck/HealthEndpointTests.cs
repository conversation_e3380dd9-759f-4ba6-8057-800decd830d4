using System.Net.Http;
using System.Threading.Tasks;
using WebAPI.Functional.Tests.Fixtures;

namespace WebAPI.Functional.Tests.HealthCheck;

public class HealthEndpointTests : IClassFixture<FunctionalTestWebApplicationFactory>
{
    private readonly HttpClient _client;

    public HealthEndpointTests(FunctionalTestWebApplicationFactory factory)
    {
        _client = factory.HttpClient;
    }

    [Fact]
    public async Task Health_When_Should()
    {
        // Arrange

        // Act
        var response = await _client.GetAsync("/health/live");

        // Assert
        response.IsSuccessStatusCode.Should().BeTrue();
    }
}
