#nullable enable
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using SharedKernel.Configurations;

namespace SharedKernel.Services;

public class TokenService : ITokenProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IConfiguration _configuration;

    public TokenService(IHttpContextAccessor httpContextAccessor, IConfiguration configuration)
    {
        _httpContextAccessor = httpContextAccessor;
        _configuration = configuration;
    }

    public string? GetBearerToken()
    {
        var context = _httpContextAccessor.HttpContext;

        if (context?.Request.Headers.TryGetValue("Authorization", out var token) == true)
        {
            var bearerToken = token.ToString();
            if (bearerToken.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                // Extract the token value after "Bearer " prefix
                return bearerToken["Bearer ".Length..];
            }
        }

        return null; // Token is missing or malformed
    }

    public (bool isValid, ClaimsPrincipal? principal, string? errorMessage) ValidateToken(string token)
    {
        // Load JWT settings from configuration
        var jwtSettings = _configuration.GetSection(nameof(JwtConfigurations)).Get<JwtConfigurations>();

        if (string.IsNullOrEmpty(jwtSettings?.Issuer) || string.IsNullOrEmpty(jwtSettings.Audience) || string.IsNullOrEmpty(jwtSettings.Key))
        {
            return (false, null, "JWT configuration settings are missing.");
        }

        try
        {
            // Define the validation parameters
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidIssuer = jwtSettings.Issuer,

                ValidateAudience = true,
                ValidAudience = jwtSettings.Audience,

                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.Key)),

                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero // Optional: Reduce default 5-minute tolerance for token expiration
            };

            var tokenHandler = new JwtSecurityTokenHandler();

            // Validate the token
            var principal = tokenHandler.ValidateToken(token, validationParameters, out _);
            return (true, principal, null);
        }
        catch (SecurityTokenException ex)
        {
            // Handle validation failure
            return (false, null, $"Token validation failed: {ex.Message}");
        }
        catch (ArgumentException ex)
        {
            // Handle argument-related exceptions
            return (false, null, $"An error occurred during token validation: {ex.Message}");
        }
        catch (Exception ex) when (ex is NullReferenceException or InvalidOperationException)
        {
            // Handle other known exceptions
            return (false, null, $"An error occurred during token validation: {ex.Message}");
        }
    }
}

public interface ITokenProvider
{
    /// <summary>
    /// Retrieves the Bearer token from the current HTTP context.
    /// </summary>
    /// <returns>The token or null if it is missing or invalid.</returns>
    string? GetBearerToken();

    /// <summary>
    /// Validates a JWT Bearer token and returns the validation result.
    /// </summary>
    /// <param name="token">The JWT token to validate.</param>
    /// <returns>A tuple indicating if the token is valid, the claims principal, and an optional error message.</returns>
    (bool isValid, ClaimsPrincipal? principal, string? errorMessage) ValidateToken(string token);
}
