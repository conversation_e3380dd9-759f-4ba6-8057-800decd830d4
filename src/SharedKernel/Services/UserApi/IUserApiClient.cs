using SharedKernel.Primitives.Contracts;
using static SharedKernel.Services.UserApi.UserApiClient;

namespace SharedKernel.Services.UserApi;
public interface IUserApiClient
{
    Task<Result<CompanyResponse>> GetCompanyName(Guid companyId, CancellationToken cancellationToken);
    Task<Result<List<UserDto>>> GetUserByEmail(List<string> emails, CancellationToken cancellationToken);
    Task<Result<List<UserDto>>> GetUserByIds(List<string> ids, CancellationToken cancellationToken);
}
