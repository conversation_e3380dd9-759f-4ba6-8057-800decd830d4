using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using SharedKernel.Primitives.Abstract;
using SharedKernel.Primitives.Contracts;

namespace SharedKernel.Services.UserApi;
public sealed class UserApiClient : BaseApiClient, IUserApiClient
{
    private readonly HttpClient _clientFactory;
    public const string ClientName = "user-api";
    private const string GetUsersByEmailRoute = "users/by-emails";
    private const string GetUsersByIdsRoute = "users/by-ids";

    public UserApiClient(IHttpClientFactory clientFactory, ITokenProvider tokenProvider) : base(tokenProvider)
    {
        _clientFactory = clientFactory.CreateClient(ClientName);
    }
    public sealed record CompanyResponse(string Name);
    public async Task<Result<CompanyResponse>> GetCompanyName(Guid companyId, CancellationToken cancellationToken)
    {
        var tokenResult = GetAndValidateToken();
        if (tokenResult.IsFailed)
        {
            // Return the token validation failure reason
            return Result.Fail<CompanyResponse>(tokenResult.Errors[0].Message);
        }

        _clientFactory.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenResult.Value);

        var httpResponseMessage = await _clientFactory.GetAsync($"companies/{companyId}", cancellationToken);

        return await HandleResponse<CompanyResponse>(httpResponseMessage, cancellationToken);
    }

    public async Task<Result<List<UserDto>>> GetUserByEmail(List<string> emails, CancellationToken cancellationToken)
    {
        if (emails == null || emails.Count == 0)
        {
            return Result.Fail<List<UserDto>>("The email list cannot be null or empty.");
        }

        var tokenResult = GetAndValidateToken();
        if (tokenResult.IsFailed)
        {
            // Return the token validation failure reason
            return Result.Fail<List<UserDto>>(tokenResult.Errors[0].Message);
        }

        try
        {
            _clientFactory.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenResult.Value);

            var payload = new { emails };

            var httpResponseMessage = await _clientFactory.PostAsJsonAsync(GetUsersByEmailRoute, payload, cancellationToken);

            if (httpResponseMessage.IsSuccessStatusCode)
            {
                try
                {
                    var result = await HandleResponse<List<UserDto>>(httpResponseMessage, cancellationToken);

                    if (result?.Value == null || result.Value.Count == 0)
                    {
                        return Result.Fail<List<UserDto>>("The response was valid but contains no data.");
                    }

                    return result; // Assume result fits the current project definition of success
                }
                catch (JsonException ex)
                {
                    return Result.Fail<List<UserDto>>($"Response parsing failed: {ex.Message}");
                }
            }

            return Result.Fail<List<UserDto>>($"Request failed with status code: {httpResponseMessage.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            // Return a failure result with the exception message
            return Result.Fail<List<UserDto>>($"An HTTP error occurred: {ex.Message}");
        }
        catch (TaskCanceledException ex)
        {
            // Return a failure result for cancellation or timeout
            return Result.Fail<List<UserDto>>($"The request was canceled or timed out: {ex.Message}");
        }
    }

    public async Task<Result<List<UserDto>>> GetUserByIds(List<string> ids, CancellationToken cancellationToken)
    {
        if (ids == null || ids.Count == 0)
        {
            return Result.Fail<List<UserDto>>("The list of ids cannot be null or empty.");
        }

        var tokenResult = GetAndValidateToken();
        if (tokenResult.IsFailed)
        {
            // Return the token validation failure reason
            return Result.Fail<List<UserDto>>(tokenResult.Errors[0].Message);
        }

        try
        {
            _clientFactory.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenResult.Value);

            var payload = new { userIds = ids };


            var httpResponseMessage = await _clientFactory.PostAsJsonAsync(GetUsersByIdsRoute, payload, cancellationToken);

            if (httpResponseMessage.IsSuccessStatusCode)
            {
                try
                {
                    var result = await HandleResponse<List<UserDto>>(httpResponseMessage, cancellationToken);

                    if (result.Value == null || result.Value.Count == 0)
                    {
                        return Result.Fail<List<UserDto>>("The response was valid but contains no data.");
                    }

                    return result; // Assume result fits the current project definition of success
                }
                catch (JsonException ex)
                {
                    return Result.Fail<List<UserDto>>($"Response parsing failed: {ex.Message}");
                }
            }

            return Result.Fail<List<UserDto>>($"Request failed with status code: {httpResponseMessage.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            // Return a failure result with the exception message
            return Result.Fail<List<UserDto>>($"An HTTP error occurred: {ex.Message}");
        }
        catch (TaskCanceledException ex)
        {
            // Return a failure result for cancellation or timeout
            return Result.Fail<List<UserDto>>($"The request was canceled or timed out: {ex.Message}");
        }
    }
}
