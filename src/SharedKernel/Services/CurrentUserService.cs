using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace SharedKernel.Services;

public interface ICurrentUserService
{
    string Id { get; }
    string IdentityId { get; }
}

public class CurrentUserService : ICurrentUserService
{
    public CurrentUserService(IHttpContextAccessor httpContextAccessor)
    {
        Id = httpContextAccessor.HttpContext?.User.FindFirstValue("user_id");
        IdentityId = httpContextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.NameIdentifier);
    }

    public string Id { get; }
    public string IdentityId { get; }
}
