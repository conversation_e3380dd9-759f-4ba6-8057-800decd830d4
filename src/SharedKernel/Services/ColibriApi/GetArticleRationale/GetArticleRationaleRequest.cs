using System.Text.Json.Serialization;

namespace SharedKernel.Services.ColibriApi.GetArticleRationale;

public sealed record GetArticleRationaleRequest
{
    public GetArticleRationaleRequest(
        string clientId,
        string clientName,
        string clientProfile,
        string modelName,
        string article)
    {
        ClientId = clientId;
        ClientName = clientName;
        ClientProfile = clientProfile;
        ModelName = modelName;
        Article = article;
    }

    [JsonPropertyName("client_id")]
    public string ClientId { get; init; }

    [JsonPropertyName("client_name")]
    public string ClientName { get; init; }

    [JsonPropertyName("client_profile")]
    public string ClientProfile { get; init; }

    [Json<PERSON>ropertyName("model_name")]
    public string ModelName { get; init; }

    [JsonPropertyName("article")]
    public string Article { get; init; }
};
