using System.Text.Json.Serialization;

namespace SharedKernel.Services.ColibriApi.AddFewShots;

public sealed record AddFewShotsResponse
{
    [JsonPropertyName("generated_fewshots")]
    public List<FewshotResponse> GeneratedFewshots { get; init; }

    [JsonPropertyName("error_fewshots")]
    public List<FewshotResponse> ErrorFewShots { get; init; }

    [JsonPropertyName("previously_existing_fewshots")]
    public List<FewshotResponse> PreviouslyExistingFewShots { get; init; }
}

public sealed record FewshotResponse
{
    public int Id { get; init; }
    public string Heading { get; init; }
    public string Url { get; init; }
    public string Error { get; init; }
}
