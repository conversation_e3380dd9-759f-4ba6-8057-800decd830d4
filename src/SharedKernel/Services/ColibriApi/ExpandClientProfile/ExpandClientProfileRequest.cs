using System.Text.Json.Serialization;

namespace SharedKernel.Services.ColibriApi.ExpandClientProfile;

public sealed record ExpandClientProfileRequest
{
    public ExpandClientProfileRequest(string clientId, string clientName, string clientProfile, string modelName = "gpt-4o")
    {
        ClientId = clientId;
        ClientName = clientName;
        ClientProfile = clientProfile;
        ModelName = modelName;
    }

    [JsonPropertyName("client_id")]
    public string ClientId { get; init; }

    [JsonPropertyName("client_name")]
    public string ClientName { get; init; }

    [JsonPropertyName("client_profile")]
    public string ClientProfile { get; init; }

    [JsonPropertyName("model_name")]
    public string ModelName { get; init; }
};
