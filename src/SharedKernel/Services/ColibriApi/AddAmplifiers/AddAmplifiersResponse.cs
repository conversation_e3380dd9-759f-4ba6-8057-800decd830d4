using System.Text.Json.Serialization;

namespace SharedKernel.Services.ColibriApi.AddAmplifiers;

public sealed record AddAmplifiersResponse
{
    [JsonPropertyName("generated_amplifiers")]
    public List<GeneratedAmplifierResponse> GeneratedAmplifiers { get; init; }

    [JsonPropertyName("error_amplifiers")]
    public List<GeneratedAmplifierResponse> ErrorAmplifiers { get; init; }

    [JsonPropertyName("previously_existing_amplifiers")]
    public List<GeneratedAmplifierResponse> PreviouslyExistingAmplifiers { get; init; }
}

public sealed record GeneratedAmplifierResponse
{
    public string Term { get; init; }
    public string Status { get; init; }
}
