using System.Text.Json.Serialization;

namespace SharedKernel.Services.ColibriApi.GetTopNews;

public sealed record GetTopNewsResponse
{
    public List<TopNewsResponse> Result { get; init; }

    [JsonPropertyName("number_of_results")]
    public int NumberOfResults { get; init; }
    public string Query { get; init; }

    [JsonPropertyName("client_id")]
    public string ClientId { get; init; }

    [JsonPropertyName("client_name")]
    public string ClientName { get; init; }
}

public sealed record TopNewsResponse
{
    public int Id { get; init; }
    public string Heading { get; init; }
    public string Contents { get; init; }
    public string Analysis { get; init; }
    public string Typecode { get; init; }
    public string Url { get; init; }
    [JsonPropertyName("n_of_appearence")]
    public int NOfAppearance { get; init; }
    public double Score { get; init; }
    public Metadata Metadata { get; init; }
}

public sealed record Metadata
{
    public string Region { get; init; }
    public double Timestamp { get; init; }
    public string TypeCode { get; init; }
    public bool IsTweet { get; init; }
    public List<string> Organisations { get; init; }
    public List<string> People { get; init; }
    public double PoliticalId { get; init; }
    public List<string> Programmes { get; init; }
}
