using System.Text.Json.Serialization;

namespace SharedKernel.Services.ColibriApi.GetTopNews;

public sealed record GetTopNewsRequest
{
    public GetTopNewsRequest(string clientId, string clientName, string query)
    {
        ClientId = clientId;
        ClientName = clientName;
        Query = query;
    }

    public GetTopNewsRequest(string clientId, string clientName, int queryId)
    {
        ClientId = clientId;
        ClientName = clientName;
        QueryId = queryId;
    }

    [JsonPropertyName("client_id")]
    public string ClientId { get; init; }

    [JsonPropertyName("client_name")]
    public string ClientName { get; init; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public string Query { get; init; }
    [JsonPropertyName("prompt_id")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int QueryId { get; init; }

    [JsonPropertyName("typecode_list")]
    public List<string> TypeCodesToInclude { get; init; }

    [JsonPropertyName("typecodes_to_exclude")]
    public List<string> TypeCodesToExclude { get; init; }

    [JsonPropertyName("amplifiers_terms")]
    public List<string> AmplifiersTerms { get; init; }

    [JsonPropertyName("amplifiers_weight")]
    public double AmplifiersWeight { get; init; }

    [JsonPropertyName("fewshot_ids")]
    public List<long> FewshotsIds { get; init; }

    [JsonPropertyName("fewshot_weight")]
    public double FewshotsWeight { get; init; }

    [JsonPropertyName("people")]
    public List<string> PeopleToInclude { get; init; }

    [JsonPropertyName("people_to_exclude")]
    public List<string> PeopleToExclude { get; init; }

    [JsonPropertyName("organisations")]
    public List<string> OrganisationsToInclude { get; init; }

    [JsonPropertyName("organisations_to_exclude")]
    public List<string> OrganisationsToExclude { get; init; }

    [JsonPropertyName("programmes")]
    public List<string> ProgrammesToInclude { get; init; }

    [JsonPropertyName("programmes_to_exclude")]
    public List<string> ProgrammesToExclude { get; init; }

    [JsonPropertyName("themes")]
    public List<string> ThemesToInclude { get; init; }

    [JsonPropertyName("themes_to_exclude")]
    public List<string> ThemesToExclude { get; init; }

    [JsonPropertyName("beginning_timestamp")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public DateTime? BegginingTimestamp { get; set; }

    [JsonPropertyName("end_timestamp")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public DateTime? EndTimestamp { get; set; }

    [JsonPropertyName("score_threshold")]
    public double ScoreThreshold { get; init; }

    [JsonPropertyName("add_metadata")]
    public bool IncludeMetadata { get; init; } = true;
}
