using System.Text.Json;

namespace SharedKernel.Services.ColibriApi.UniqueAiMetadata;

public sealed record UniqueAiMetadata(
    ICollection<string> People,
    ICollection<string> Organisations,
    ICollection<string> Programmes);

public interface IUniqueAiMetadataConverter
{
    UniqueAiMetadata ConvertToUniqueAiMetadataResponse(
        UniqueAiMetadataResponse response);
}

public sealed class UniqueAiMetadataConverter : IUniqueAiMetadataConverter
{
    public UniqueAiMetadata ConvertToUniqueAiMetadataResponse(
        UniqueAiMetadataResponse response)
    {
        if (response is null)
        {
            return new UniqueAiMetadata(
                new List<string>(),
                new List<string>(),
                new List<string>());
        }

        var people = new List<string>();
        var programmes = new List<string>();
        var organisations = new List<string>();

        if (response.People is not null)
        {
            people.AddRange(from item in response.People where item.ValueKind == JsonValueKind.String select item.GetString()!);
        }

        if (response.Organisations is not null)
        {
            organisations.AddRange(from item in response.Organisations where item.ValueKind == JsonValueKind.String select item.GetString()!);
        }

        if (response.Programmes is not null)
        {
            programmes.AddRange(from item in response.Programmes where item.ValueKind == JsonValueKind.String select item.GetString()!);
        }

        return new UniqueAiMetadata(
            people,
            organisations,
            programmes);
    }
}
