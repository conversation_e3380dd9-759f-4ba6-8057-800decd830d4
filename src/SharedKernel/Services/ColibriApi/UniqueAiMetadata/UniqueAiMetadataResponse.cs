using System.Text.Json;
using System.Text.Json.Serialization;

namespace SharedKernel.Services.ColibriApi.UniqueAiMetadata;

public sealed record UniqueAiMetadataResponse
{
    [JsonPropertyName("organisations")]
    public IList<JsonElement> Organisations { get; init; } = new List<JsonElement>();

    [JsonPropertyName("people")]
    public IList<JsonElement> People { get; init; } = new List<JsonElement>();

    [JsonPropertyName("programmes")]
    public IList<JsonElement> Programmes { get; init; } = new List<JsonElement>();
};
