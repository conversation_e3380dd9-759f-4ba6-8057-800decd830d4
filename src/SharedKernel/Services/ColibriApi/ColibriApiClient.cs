using System.Net.Http.Json;
using SharedKernel.Primitives.Abstract;
using SharedKernel.Services.ColibriApi.AddAmplifiers;
using SharedKernel.Services.ColibriApi.AddFewShots;
using SharedKernel.Services.ColibriApi.ExpandClientProfile;
using SharedKernel.Services.ColibriApi.GetArticleRationale;
using SharedKernel.Services.ColibriApi.GetTopNews;
using SharedKernel.Services.ColibriApi.UniqueAiMetadata;

namespace SharedKernel.Services.ColibriApi;

public interface IColibriApi
{
    Task<Result<AddAmplifiersResponse>> AddAmplifiers(AddAmplifiersRequest request, CancellationToken cancellationToken);
    Task<Result<AddFewShotsResponse>> AddFewShots(AddFewShotsRequest request, CancellationToken cancellationToken);
    Task<Result<ExpandClientProfileResponse>> ExpandClientProfileAsync(ExpandClientProfileRequest request, CancellationToken cancellationToken);
    Task<Result<GetArticleRationaleResponse>> GetArticleRationaleAsync(GetArticleRationaleRequest request, CancellationToken cancellationToken);
    Task<Result<GetTopNewsResponse>> GetTopNewsAsync(GetTopNewsRequest request, CancellationToken cancellationToken);
    Task<Result<UniqueAiMetadataResponse>> GetUniqueAiMetadataAsync(CancellationToken cancellationToken);
}

public sealed class ColibriApiClient : BaseApiClient, IColibriApi
{
    public const string ClientName = "colibri-api";
    private readonly HttpClient _httpClient;

    public ColibriApiClient(IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient(ClientName);
    }

    public async Task<Result<AddAmplifiersResponse>> AddAmplifiers(AddAmplifiersRequest request, CancellationToken cancellationToken)
    {
        var httpResponseMessage = await _httpClient.PostAsJsonAsync("add-amplifiers/", request, cancellationToken);

        return await HandleResponse<AddAmplifiersResponse>(httpResponseMessage, cancellationToken);
    }

    public async Task<Result<AddFewShotsResponse>> AddFewShots(AddFewShotsRequest request, CancellationToken cancellationToken)
    {
        var httpResponseMessage = await _httpClient.PostAsJsonAsync("add-few-shots/", request, cancellationToken);

        return await HandleResponse<AddFewShotsResponse>(httpResponseMessage, cancellationToken);
    }

    public async Task<Result<ExpandClientProfileResponse>> ExpandClientProfileAsync(
        ExpandClientProfileRequest request,
        CancellationToken cancellationToken)
    {
        using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        cts.CancelAfter(TimeSpan.FromSeconds(30));

        try
        {
            var httpResponseMessage = await _httpClient.PostAsJsonAsync(
                "expand-client-profile/",
                request,
                cts.Token
            );

            return await HandleResponse<ExpandClientProfileResponse>(httpResponseMessage, cts.Token);
        }
        catch (OperationCanceledException) when (!cancellationToken.IsCancellationRequested)
        {
            // Timeout occurred (logging can be added as needed)
            return Result.Fail<ExpandClientProfileResponse>(new Error("The operation timed out after 30 seconds."));
        }
    }

    public async Task<Result<GetArticleRationaleResponse>> GetArticleRationaleAsync(GetArticleRationaleRequest request, CancellationToken cancellationToken)
    {
        var httpResponseMessage = await _httpClient.PostAsJsonAsync("get-article-rationale/", request, cancellationToken);

        return await HandleResponse<GetArticleRationaleResponse>(httpResponseMessage, cancellationToken);
    }

    public async Task<Result<GetTopNewsResponse>> GetTopNewsAsync(GetTopNewsRequest request, CancellationToken cancellationToken)
    {
        var httpResponseMessage = await _httpClient.PostAsJsonAsync("get-top-news/", request, cancellationToken);

        return await HandleResponse<GetTopNewsResponse>(httpResponseMessage, cancellationToken);
    }

    public async Task<Result<UniqueAiMetadataResponse>> GetUniqueAiMetadataAsync(CancellationToken cancellationToken)
    {
        var httpResponseMessage = await _httpClient.GetAsync("unique-ai-metadata/", cancellationToken);

        return await HandleResponse<UniqueAiMetadataResponse>(httpResponseMessage, cancellationToken);
    }
}
