using Microsoft.Extensions.Caching.Memory;
using SharedKernel.Primitives.Interfaces;

namespace SharedKernel.Services;

public class MemoryCacheService : ICacheService
{
    private readonly IMemoryCache _cache;

    public MemoryCacheService(IMemoryCache cache)
    {
        _cache = cache;
    }

    public Task<T> GetAsync<T>(string key)
    {
        if (_cache.TryGetValue(key, out T value))
        {
            return Task.FromResult(value);
        }

        return Task.FromResult<T>(default);
    }

    public Task SetAsync<T>(string key, T value, TimeSpan? absoluteExpirationRelativeToNow = null)
    {
        var options = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = absoluteExpirationRelativeToNow
        };

        _cache.Set(key, value, options);

        return Task.CompletedTask;
    }

    public async Task RemoveAsync(string key)
    {
        if (_cache.TryGetValue(key, out _))
        {
            _cache.Remove(key);
        }

        await Task.CompletedTask;
    }
}
