using OpenSearch.Client;

namespace SharedKernel.Services;

public sealed record ElasticSearchContent(
    int PoliticalId,
    int? TypeCode,
    string TypeCodeName,
    string Heading,
    string Content);

public interface IOpenSearchService
{
    Task<ElasticSearchContent> GetById(int id, CancellationToken cancellationToken);
}

public sealed class OpenSearchService : IOpenSearchService
{
    private readonly IOpenSearchClient _openSearchClient;

    public OpenSearchService(IOpenSearchClient openSearchClient)
    {
        _openSearchClient = openSearchClient;
    }

    public async Task<ElasticSearchContent> GetById(int id, CancellationToken cancellationToken)
    {
        var streamItem = await _openSearchClient.SearchAsync<ElasticSearchContent>(c => c
            .Index(_openSearchClient.ConnectionSettings.DefaultIndex)
            .Query(q => q
                .Match(m => m
                    .<PERSON>("politicalId")
                    .Query(id.ToString())
                )
            ), cancellationToken);

        return streamItem.Documents.FirstOrDefault();
    }
}
