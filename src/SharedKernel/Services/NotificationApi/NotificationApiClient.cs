using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using SharedKernel.Primitives.Abstract;
using SharedKernel.Primitives.Contracts;

namespace SharedKernel.Services.NotificationApi;
public sealed class NotificationApiClient : BaseApiClient, INotificationApiClient
{
    private readonly HttpClient _clientFactory;
    public const string ClientName = "notification-api";
    private const string GetDistributionGroupsByIdsRoute = "distribution-groups/by-ids";

    public NotificationApiClient(IHttpClientFactory clientFactory, ITokenProvider tokenProvider) : base(tokenProvider)
    {
        _clientFactory = clientFactory.CreateClient(ClientName);
    }

    public async Task<Result<List<DistributionGroupDto>>> GetDistributionGroupsAsync(List<Guid> distributionGroupIds, CancellationToken cancellationToken)
    {
        try
        {
            if (distributionGroupIds == null || distributionGroupIds.Count == 0)
            {
                return Result.Fail<List<DistributionGroupDto>>("The email list cannot be null or empty.");
            }

            var tokenResult = GetAndValidateToken();
            if (tokenResult.IsFailed)
            {
                // Return the token validation failure reason
                return Result.Fail<List<DistributionGroupDto>>(tokenResult.Errors[0].Message);
            }

            var payload = new { distributionGroupIds };

            _clientFactory.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenResult.Value);

            var httpResponseMessage = await _clientFactory.PostAsJsonAsync(GetDistributionGroupsByIdsRoute, payload, cancellationToken);

            if (httpResponseMessage.IsSuccessStatusCode)
            {
                try
                {
                    var result = await HandleResponse<List<DistributionGroupDto>>(httpResponseMessage, cancellationToken);

                    if (result?.Value == null || result.Value.Count == 0)
                    {
                        return Result.Fail<List<DistributionGroupDto>>("The response was valid but contains no data.");
                    }

                    return result; // Assume result fits the current project definition of success
                }
                catch (JsonException ex)
                {
                    return Result.Fail<List<DistributionGroupDto>>($"Response parsing failed: {ex.Message}");
                }
            }

            return Result.Fail<List<DistributionGroupDto>>($"Request failed with status code: {httpResponseMessage.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            // Return a failure result with the exception message
            return Result.Fail<List<DistributionGroupDto>>($"An HTTP error occurred: {ex.Message}");
        }
        catch (TaskCanceledException ex)
        {
            // Return a failure result for cancellation or timeout
            return Result.Fail<List<DistributionGroupDto>>($"The request was canceled or timed out: {ex.Message}");
        }
    }
}
