using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public readonly record struct AiModelId(Guid Value);

public class AiModel : BaseEntity
{
    protected AiModel()
    { }

    protected AiModel(string name)
    {
        Id = new AiModelId(BaseEntityIdentifier.Create());
        Name = name;
    }

    public AiModelId Id { get; private set; }
    public string Name { get; private set; }
    public double Confidence { get; private set; }

    public IReadOnlyCollection<Stream> Streams { get; private set; }

    public void Update(string name, double confidence)
    {
        Name = name;
        Confidence = confidence;
    }

    public static AiModel Create(string name)
    {
        return new AiModel(name);
    }
}
