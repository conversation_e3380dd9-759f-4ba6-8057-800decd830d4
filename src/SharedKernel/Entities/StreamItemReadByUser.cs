namespace SharedKernel.Entities;

public class StreamItemReadByUser
{
    protected StreamItemReadByUser()
    {

    }

    protected StreamItemReadByUser(Guid userId, int streamItemId)
    {
        UserId = userId;
        StreamItemId = streamItemId;
        ReadDateUtc = DateTimeOffset.UtcNow;
    }

    public Guid UserId { get; private set; }

    public int StreamItemId { get; private set; }

    public DateTimeOffset ReadDateUtc { get; private set; }

    public static StreamItemReadByUser Create(Guid userId, int streamItemId)
    {
        return new StreamItemReadByUser(userId, streamItemId);
    }
}
