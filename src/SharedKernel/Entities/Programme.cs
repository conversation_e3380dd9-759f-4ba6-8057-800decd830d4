using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public readonly record struct ProgrammeId(Guid Value);

public class Programme : BaseEntity
{
    protected Programme()
    { }

    protected Programme(string name)
    {
        Id = new ProgrammeId(BaseEntityIdentifier.Create());
        Name = name;
    }

    public ProgrammeId Id { get; private set; }
    public string Name { get; private set; }

    public static Programme Create(string name)
    {
        return new Programme(name);
    }

    public void Update(string name)
    {
        Name = name;
    }
}
