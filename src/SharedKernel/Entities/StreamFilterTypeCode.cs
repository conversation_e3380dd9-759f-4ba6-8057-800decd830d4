using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public record struct StreamFilterTypeCodeId(Guid Value);
public class StreamFilterTypeCode
{
    protected StreamFilterTypeCode()
    {
    }

    protected StreamFilterTypeCode(int codeId, StreamId streamId, bool includeFlag)
    {
        Id = new StreamFilterTypeCodeId(BaseEntityIdentifier.Create());
        CodeId = codeId;
        StreamId = streamId;
        IncludeFlag = includeFlag;
    }

    public StreamFilterTypeCodeId Id { get; private set; }

    public int CodeId { get; private set; }

    public TypeCode TypeCode { get; private set; }

    public StreamId StreamId { get; private set; }

    public bool IncludeFlag { get; private set; }
    public Stream Stream { get; private set; }

    public static StreamFilterTypeCode Create(int codeId, StreamId streamId, bool includeFlag)
        => new StreamFilterTypeCode(codeId, streamId, includeFlag);
}
