using OpenSearch.Client;
using SharedKernel.Primitives.Abstract;
using SharedKernel.Primitives.Contracts;

namespace SharedKernel.Entities;

public class TypeCode : BaseEntity
{
    public ICollection<StreamFilterTypeCode> StreamFilterTypeCodes { get; private set; }

    protected TypeCode()
    { }

    protected TypeCode(string name, int parentId)
    {
        ParentId = parentId;
        Name = name;
    }

    public int Id { get; private set; }
    public int ParentId { get; private set; }
    public string Name { get; private set; }

    public static TypeCode Create(string name, int parentId)
    {
        return new TypeCode(name, parentId);
    }
    public void Update(
        string name, int parentId)
    {
        Name = name;
        ParentId = parentId;
    }

    public void Delete()
    {
        IsDeleted = true;
    }
}
