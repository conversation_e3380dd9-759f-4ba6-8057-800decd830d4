using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public readonly record struct PeopleId(Guid Value);
public record FirstName(string Value);
public record Surname(string Value);

public class People : BaseEntity
{
    protected People()
    { }

    protected People(FirstName firstName, Surname surname)
    {
        Id = new PeopleId(BaseEntityIdentifier.Create());
        FirstName = firstName.Value;
        Surname = surname.Value;
    }

    public PeopleId Id { get; private set; }
    public string FirstName { get; private set; }
    public string Surname { get; private set; }

    public static People Create(FirstName firstName, Surname surname)
    {
        return new People(firstName, surname);
    }

    public void Update(string firstName, string surname)
    {
        FirstName = firstName;
        Surname = surname;
    }
}
