using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public record struct FewShotId(Guid Value);

public class FewShot
{
    protected FewShot()
    {

    }

    protected FewShot(StreamId streamId, long itemId, double weight)
    {
        Id = new FewShotId(BaseEntityIdentifier.Create());
        StreamId = streamId;
        ItemId = itemId;
        Weight = weight;
    }

    public FewShotId Id { get; private set; }

    public StreamId StreamId { get; private set; }

    public Stream Stream { get; private set; }

    public long ItemId { get; private set; }

    public double Weight { get; private set; }

    public static FewShot Create(
        StreamId streamId,
        long itemId,
        double weight) => new FewShot(streamId, itemId, weight);
}
