using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public readonly record struct StreamId(Guid Value);

public class Stream : BaseEntity
{
    private readonly List<StreamMetadataFilter> _streamMetadataFilters = [];
    private readonly List<StreamAlert> _streamAlerts = [];

    protected Stream()
    {
    }

    protected Stream(
        Guid? companyId,
        AiModelId aiModelId,
        string name,
        string prompt,
        string companyName,
        bool? companySpecificStream,
        string aiGeneratedPrompt,
        int aiGeneratedPromptId,
        double aiModelConfidence,
        int noOfArticles,
        bool isMention = false
    )
    {
        Id = new StreamId(BaseEntityIdentifier.Create());
        CompanyId = companyId;
        AiModelId = aiModelId;
        Name = name;
        Prompt = prompt;
        CompanyName = companyName;
        CompanySpecificStream = companySpecificStream;
        AiGeneratedPrompt = aiGeneratedPrompt;
        AiGeneratedPromptId = aiGeneratedPromptId;
        AiModelConfidence = aiModelConfidence;
        NoOfArticles = noOfArticles;
        IsMention = isMention;
    }

    protected Stream(
        string name,
        Guid? companyId,
        string companyName,
        bool? companySpecificStream,
        int noOfArticles,
        bool isMention = true
    )
    {
        Id = new StreamId(BaseEntityIdentifier.Create());
        CompanyId = companyId;
        Name = name;
        CompanyName = companyName;
        CompanySpecificStream = companySpecificStream;
        NoOfArticles = noOfArticles;
        IsMention = isMention;
    }

    public StreamId Id { get; private set; }

    public Guid? CompanyId { get; private set; }

    public string Name { get; private set; }

    public string Prompt { get; private set; }

    public string CompanyName { get; private set; }

    public int? NoOfArticles { get; private set; }

    public bool? CompanySpecificStream { get; private set; }

    public bool IsMention { get; private set; }
    public bool IsFeed { get; private set; }

    public string AiGeneratedPrompt { get; private set; }
    public int? AiGeneratedPromptId { get; private set; }

    public double AiModelConfidence { get; private set; }

    public AiModelId? AiModelId { get; private set; }
    public AiModel AiModel { get; private set; }

    public IReadOnlyCollection<Amplifier> Amplifiers { get; private set; }
    public IReadOnlyCollection<FewShot> FewShots { get; private set; }
    public IReadOnlyCollection<StreamAlert> StreamAlerts => _streamAlerts;
    public IReadOnlyCollection<StreamMetadataFilter> StreamMetadataFilters => _streamMetadataFilters;
    public IReadOnlyCollection<StreamFilterTypeCode> StreamFilterTypeCodes { get; private set; }

    public void AddStreamMetadataFilters(List<StreamMetadataFilter> streamMetadataFilter)
    {
        _streamMetadataFilters.AddRange(streamMetadataFilter);
    }

    public void Update(
        Guid? companyId,
        AiModelId aiModelId,
        string name,
        string prompt,
        string companyName,
        bool? companySpecificStream,
        string aiGeneratedPrompt,
        int aiGeneratedPromptId,
        double aiModelConfidence,
        int noOfArticles = 50
    )
    {
        CompanyId = companyId;
        AiModelId = aiModelId;
        Name = name;
        Prompt = prompt;
        CompanyName = companyName;
        CompanySpecificStream = companySpecificStream;
        AiGeneratedPrompt = aiGeneratedPrompt;
        AiGeneratedPromptId = aiGeneratedPromptId;
        AiModelConfidence = aiModelConfidence;
        NoOfArticles = noOfArticles;
    }

    public void UpdateMention(string name, Guid? companyId, string companyName, bool? companySpecificStream,
        int noOfArticles = 50)
    {
        CompanyId = companyId;
        Name = name;
        CompanyName = companyName;
        CompanySpecificStream = companySpecificStream;
        NoOfArticles = noOfArticles;
    }

    public static Stream Create(
        Guid? companyId,
        AiModelId aiModelId,
        string name,
        string prompt,
        string companyName,
        bool? companySpecificStream,
        string aiGeneratedPrompt,
        int aiGeneratedPromptId,
        double aiModelConfidence,
        int noOfArticles = 50,
        bool isMention = false
    )
    {
        return new Stream(
            companyId,
            aiModelId,
            name,
            prompt,
            companyName,
            companySpecificStream,
            aiGeneratedPrompt,
            aiGeneratedPromptId,
            aiModelConfidence,
            noOfArticles,
            isMention
        );
    }

    public static Stream CreateMention(string name, Guid? companyId, string companyName,
        bool? companySpecificStream, int noOfArticles = 50,
        bool isMention = true)
    {
        return new Stream(
            name,
            companyId,
            companyName,
            companySpecificStream,
            noOfArticles,
            isMention
        );
    }
}
