using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public record struct AmplifierId(Guid Value);

public class Amplifier
{
    protected Amplifier()
    { }

    protected Amplifier(StreamId streamId, string name, double weight)
    {
        Id = new AmplifierId(BaseEntityIdentifier.Create());
        StreamId = streamId;
        Name = name;
        Weight = weight;
    }

    public AmplifierId Id { get; private set; }

    public StreamId StreamId { get; private set; }

    public Stream Stream { get; private set; }

    public string Name { get; private set; }

    public double Weight { get; private set; }

    public static Amplifier Create(StreamId streamId, string name, double weight)
    {
        return new Amplifier(streamId, name, weight);
    }
}
