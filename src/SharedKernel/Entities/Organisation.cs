using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public readonly record struct OrganisationId(Guid Value);

public class Organisation : BaseEntity
{
    protected Organisation()
    { }

    protected Organisation(string name)
    {
        Id = new OrganisationId(BaseEntityIdentifier.Create());
        Name = name;
    }

    public OrganisationId Id { get; private set; }
    public string Name { get; private set; }

    public static Organisation Create(string name)
    {
        return new Organisation(name);
    }

    public void UpdateName(string name)
    {
        Name = name;
    }
}
