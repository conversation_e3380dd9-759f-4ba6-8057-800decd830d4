using System.Runtime.Serialization;
using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public record struct StreamFilterId(Guid Value);

public enum StreamFilterTableName
{
    [EnumMember(Value = "people")]
    People,

    [EnumMember(Value = "programme")]
    Programme,

    [EnumMember(Value = "organisation")]
    Organisation,

    [EnumMember(Value = "type Code")]
    TypeCode
}

public class StreamFilter
{
    protected StreamFilter()
    { }

    protected StreamFilter(StreamId streamId, Guid rowId, string tableName, bool includeFlag)
    {
        Id = new StreamFilterId(BaseEntityIdentifier.Create());
        StreamId = streamId;
        RowId = rowId;
        TableName = tableName;
        IncludeFlag = includeFlag;
    }

    public StreamFilterId Id { get; private set; }

    public StreamId StreamId { get; private set; }

    public Stream Stream { get; private set; }

    public Guid RowId { get; private set; }

    public string TableName { get; private set; }

    public bool IncludeFlag { get; private set; }

    public static StreamFilter Create(
        StreamId streamId,
        Guid rowId,
        StreamFilterTableName streamFilterTableName,
        bool includeFlag) => new StreamFilter(
            streamId,
            rowId,
            streamFilterTableName.ToString(),
            includeFlag);
}
