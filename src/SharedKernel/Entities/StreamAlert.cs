using SharedKernel.Primitives.Abstract;

namespace SharedKernel.Entities;

public readonly record struct StreamAlertId(Guid Value);

public enum StreamAlertFrequency
{
    None,
    Hour,
    Day,
    Week,
    Month
}

public enum StreamAlertRecurrence
{
    Once,
    Repeated
}

public enum StreamAlertStatus
{
    Active = 1,
    Pause = 2
}

public class StreamAlert : BaseEntity
{
    protected StreamAlert()
    { }

    protected StreamAlert(
        StreamId streamId,
        StreamAlertFrequency frequency,
        StreamAlertRecurrence recurrence,
        DateTimeOffset startDateTime,
        DateTimeOffset? endDateTime,
        Guid? templateId,
        string name,
        List<string> recipientEmails,
        Guid? distributionGroupId)
    {
        Id = new StreamAlertId(BaseEntityIdentifier.Create());
        Status = StreamAlertStatus.Active;
        StreamId = streamId;
        Frequency = frequency;
        Recurrence = recurrence;
        StartDateTime = startDateTime;
        EndDateTime = endDateTime;
        TemplateId = templateId;
        Name = name;
        EmailRecipients = string.Join(",", recipientEmails);
        DistributionGroupId = distributionGroupId;
    }

    public StreamAlertId Id { get; private set; }
    public string Name { get; private set; }
    public string EmailRecipients { get; private set; } // Stored as comma-separated emails
    public Guid? DistributionGroupId { get; private set; }
    public StreamAlertFrequency Frequency { get; private set; }
    public StreamAlertRecurrence Recurrence { get; private set; }
    public DateTimeOffset StartDateTime { get; private set; }
    public DateTimeOffset? EndDateTime { get; private set; }
    public DateTimeOffset? LastSent { get; private set; }
    public DateTimeOffset? NextAlertDate { get; private set; }
    public StreamAlertStatus Status { get; private set; }
    public string HangfireId { get; private set; }
    public Guid? TemplateId { get; private set; }

    public StreamId StreamId { get; private set; }
    public Stream Stream { get; private set; }

    public void SetHangfireId(string hangfireId)
    {
        HangfireId = hangfireId;
    }

    public void Update(
        DateTimeOffset startDateTime,
        DateTimeOffset? endDateTime,
        Guid? templateId,
        string name,
        List<string> recipientEmails,
        Guid? distributionGroupId)
    {
        Name = name;
        DistributionGroupId = distributionGroupId;
        EmailRecipients = string.Join(",", recipientEmails);
        StartDateTime = startDateTime;
        EndDateTime = endDateTime;
        TemplateId = templateId;
    }

    public void UpdateHangfireId(
        string hangfireId)
    {
        HangfireId = hangfireId;
    }

    public static StreamAlert Create(
        StreamId streamId,
        StreamAlertFrequency frequency,
        StreamAlertRecurrence recurrence,
        DateTimeOffset startDateTime,
        DateTimeOffset? endDateTime,
        Guid? templateId,
        string name,
        List<string> recipientEmails,
        Guid? distributionGroupId)
    {
        return new StreamAlert(streamId, frequency, recurrence, startDateTime, endDateTime, templateId, name,
            recipientEmails, distributionGroupId);
    }

    public void UpdateLastSent(DateTimeOffset lastSent)
    {
        LastSent = lastSent;
    }

    public void UpdateNextAlertDate(DateTimeOffset? nextAlertDate)
    {
        NextAlertDate = nextAlertDate;
    }

}
