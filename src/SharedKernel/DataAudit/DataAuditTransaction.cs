using System.Diagnostics.CodeAnalysis;
using SharedKernel.Primitives.CustomAttributes;

namespace SharedKernel.DataAudit;

[NonAuditable]
[SuppressMessage("Usage", "CA2227:Collection properties should be read only")]
public class DataAuditTransaction
{
    public long Id { get; set; }

    public string IdentityUserId { get; set; }

    public DateTimeOffset EventDateUtc { get; set; }

    public virtual ICollection<DataAuditTransactionDetail> TransactionDetails { get; set; }
}
