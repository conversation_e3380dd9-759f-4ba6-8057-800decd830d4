using Microsoft.EntityFrameworkCore;

namespace SharedKernel.DataAudit;

public static class EntityStateExtensions
{
    public static DataAuditTransactionTypes ToAuditType(this EntityState state)
    {
        return state switch
        {
            EntityState.Added => DataAuditTransactionTypes.Insert,
            EntityState.Modified => DataAuditTransactionTypes.Update,
            EntityState.Deleted => DataAuditTransactionTypes.Delete,
            _ => throw new ArgumentException("Unrecognized entity state: " + state)
        };
    }
}
