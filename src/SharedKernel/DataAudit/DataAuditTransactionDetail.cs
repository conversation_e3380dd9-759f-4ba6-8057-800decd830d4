using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SharedKernel.Primitives.CustomAttributes;

namespace SharedKernel.DataAudit;

[NonAuditable]
[Table("DataAuditTransactionDetails")]
public class DataAuditTransactionDetail
{
    public long Id { get; set; }

    public long DataAuditTransactionId { get; set; }
    public virtual DataAuditTransaction DataAuditTransaction { get; set; }

    public DataAuditTransactionTypes DataAuditTransactionType { get; set; }

    [Required]
    [MaxLength(255)]
    public string EntityName { get; set; }

    [Required]
    public string PrimaryKeyValue { get; set; }

    [Required]
    [MaxLength(255)]
    public string PropertyName { get; set; }
    public string OriginalValue { get; set; }
    public string NewValue { get; set; }
}
