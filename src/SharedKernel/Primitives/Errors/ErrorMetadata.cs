namespace SharedKernel.Primitives.Errors;

internal static class ErrorMetadata
{
    public static readonly Dictionary<string, object> Authentication = new() { { ErrorMetadataType.Authentication, "Authentication Issue" } };
    public static readonly Dictionary<string, object> NotFound = new() { { ErrorMetadataType.NotFound, "Not Found Issue" } };
    public static readonly Dictionary<string, object> Validation = new() { { ErrorMetadataType.Validation, "Validation Issue" } };
    public static readonly Dictionary<string, object> Forbidden = new() { { ErrorMetadataType.Forbidden, "Forbidden Issue" } };
}
