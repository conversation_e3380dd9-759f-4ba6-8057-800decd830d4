using System.Text.Json.Serialization;

namespace SharedKernel.Primitives.Contracts;

public class DistributionGroupResponse(Guid id)
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; } = id;
    public string Name { get; set; }
    public string Description { get; set; }
    public int TotalUsers { get; set; }
}

public class DistributionGroupId(Guid value)
{
    // Matches nested "value" in JSON
    [JsonPropertyName("value")]
    public Guid Value { get; set; } = value;
}
