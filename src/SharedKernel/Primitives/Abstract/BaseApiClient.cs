#nullable enable
using System.Net.Http.Json;
using System.Text.Json;
using SharedKernel.Services;

namespace SharedKernel.Primitives.Abstract;

public abstract class BaseApiClient
{
    private readonly ITokenProvider? _tokenProvider;

    /// <summary>
    /// Base constructor that optionally accepts a token provider.
    /// </summary>
    protected BaseApiClient(ITokenProvider? tokenProvider = null)
    {
        _tokenProvider = tokenProvider;
    }

    /// <summary>
    /// Retrieves and validates the Bearer token if a token provider is provided.
    /// Uses FluentResults to return success or error outcomes.
    /// </summary>
    protected Result<string> GetAndValidateToken()
    {
        if (_tokenProvider == null)
        {
            return Result.Fail<string>("Token provider is not available for this client.");
        }

        var token = _tokenProvider.GetBearerToken();
        if (string.IsNullOrEmpty(token))
        {
            return Result.Fail<string>("Authorization token is missing or malformed.");
        }

        var (isValid, _, errorMessage) = _tokenProvider.ValidateToken(token);
        if (!isValid)
        {
            return Result.Fail<string>($"Token validation failed: {errorMessage}");
        }

        return Result.Ok(token); // Return the valid token
    }

    protected static async Task<Result<TResponse>> HandleResponse<TResponse>(
        HttpResponseMessage httpResponse,
        CancellationToken cancellationToken)
    {
        try
        {
            var response = await httpResponse.Content.ReadFromJsonAsync<TResponse>(cancellationToken: cancellationToken);
            return response is not null
                ? Result.Ok(response)
                : Result.Fail<TResponse>(new Error("Response was null or could not be deserialized"));
        }
        catch (JsonException ex)
        {
            return Result.Fail<TResponse>(new Error($"Deserialization error: {ex.Message}"));
        }
        catch (NotSupportedException ex)
        {
            return Result.Fail<TResponse>(new Error($"Unsupported media type: {ex.Message}"));
        }
    }

    private static async Task<Result> HandleError(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
    {
        var errorResponseAsJson = await httpResponseMessage.Content.ReadAsStringAsync(cancellationToken: cancellationToken);
        var dictionary = JsonSerializer.Deserialize<Dictionary<string, List<string>>>(errorResponseAsJson);

        return Result.Fail(dictionary?.Select(e => new Error(e.Value[0], new Error(e.Key))).ToList() ?? new List<Error>());
    }
}
