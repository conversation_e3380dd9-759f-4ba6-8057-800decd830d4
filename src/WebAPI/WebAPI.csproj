<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <UserSecretsId>e09f1ba5-32f2-4b13-87c0-a5a6f7b57b4b</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.ApiEndpoints" />
    <PackageReference Include="AspNetCore.HealthChecks.Elasticsearch" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="BetterStack.Logs.Serilog" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Serilog.Enrichers.Environment" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" />
    <PackageReference Include="Serilog.Sinks.Seq" />
    <PackageReference Include="StackExchange.Redis" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SharedKernel\SharedKernel.csproj" />
  </ItemGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
      <_Parameter1>WebAPI.Unit.Tests</_Parameter1>
    </AssemblyAttribute>

    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
      <_Parameter1>DynamicProxyGenAssembly2</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Features\Programmes\GetProgrammesColibri\GetProgrammesFilterRequest.cs" />
    <Compile Remove="Features\Programmes\GetProgrammesColibri\GetProgrammesColibriQuery.cs" />
    <Compile Remove="Features\Peoples\GetPeopleColibri\GetPeopleResponse.cs" />
    <Compile Remove="Features\Peoples\GetPeopleColibri\GetPeopleFilterRequest.cs" />
    <Compile Remove="Features\Peoples\GetPeopleColibri\GetPeopleColibriQuery.cs" />
    <Compile Remove="Features\Organisations\GetOrganisationsColibri\GetOrganisationsResponse.cs" />
    <Compile Remove="Features\Organisations\GetOrganisationsColibri\GetOrganisationsFilterRequest.cs" />
    <Compile Remove="Features\Organisations\GetOrganisationsColibri\GetOrganisationsColibriQuery.cs" />
  </ItemGroup>

</Project>
