namespace WebAPI.Infrastructure.Static;

public static class InternalRole
{
    private const string SuperUser = "SuperUser";
    private const string Admin = "Admin";
    private const string SuperVisor = "SuperVisor";
    private const string Editor = "Editor";
    private const string ServiceUser = "ServiceUser";

    public static IReadOnlyList<string> GetRoles =>
    [
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON>,
        SuperVisor,
        Editor,
        ServiceUser
    ];
}
