using WebAPI.Infrastructure.Configurations;

namespace WebAPI.Infrastructure.Extensions;

internal static class WebApplicationBuilderExtensions
{
    public static WebApplicationBuilder ConfigureApplicationBuilder(this WebApplicationBuilder builder)
    {
        builder.Configuration.AddEnvironmentVariables("DH__");

        builder.Host.UseSerilog((context, configuration) =>
        {
            configuration
                .ReadFrom.Configuration(context.Configuration)
                .WithDefaultEnrich()
                .WithDefaultFilter()
                .WriteTo.Console();

            if (builder.Environment.IsDevelopment())
            {
                configuration.WriteTo.Seq("http://localhost:5341");
            }

            var betterstackSettings = context.Configuration.GetSection(nameof(Betterstack)).Get<Betterstack>();
            configuration.WriteTo.BetterStack(sourceToken: betterstackSettings.Token);
        });

        var cacheSettings = builder.Configuration
            .GetSection(nameof(Caching))
            .Get<Caching>();

        if (cacheSettings.UseDistributedCache)
        {
            builder.Services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = cacheSettings.DistributedCacheConfigurationString;
            });
        }
        else
        {
            builder.Services.AddMemoryCache();
        }

        builder.Services
            .AddDependencyInjection(builder.Configuration, builder.Environment);

        return builder;
    }
}
