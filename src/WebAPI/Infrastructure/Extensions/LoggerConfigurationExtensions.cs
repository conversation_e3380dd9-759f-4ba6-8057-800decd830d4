using Serilog.Filters;

namespace WebAPI.Infrastructure.Extensions;

internal static class LoggerConfigurationExtensions
{
    public static LoggerConfiguration WithDefaultEnrich(this LoggerConfiguration loggerConfiguration)
        => loggerConfiguration.Enrich.WithEnvironmentName()
            .Enrich.WithPodName()
            .Enrich.WithApplicationVersion()
            .Enrich.WithApplicationName();

    public static LoggerConfiguration WithDefaultFilter(this LoggerConfiguration loggerConfiguration) =>
        loggerConfiguration
            .Filter.ByExcluding(Matching.WithProperty<string>("RequestMethod", method => method == "OPTIONS"))
            .Filter.ByExcluding(Matching.WithProperty<string>("RequestPath", path => path == "/")) // Existing
            .Filter.ByExcluding(Matching.WithProperty<string>("RequestPath", path =>
                path is "/health/live" or "/health/ready")); // Added filter for health checks
}
