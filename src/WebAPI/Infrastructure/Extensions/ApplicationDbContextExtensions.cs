using WebAPI.Persistence;

namespace WebAPI.Infrastructure.Extensions;

public static class ApplicationDbContextExtensions
{
    public static async Task<IList<int>> GetInflatedTypeCodesAsync(this ApplicationDbContext dbContext,
        IEnumerable<int> typeCodeIds,
        CancellationToken cancellationToken)
    {
        var inflatedTypeCodeIds = new List<int>();

        foreach (var id in typeCodeIds)
        {
            var children = await dbContext.TypeCodes
                .Where(x => x.ParentId == id)
                .Select(x => x.Id)
                .ToListAsync(cancellationToken);

            if (children.Count > 0)
            {
                inflatedTypeCodeIds.AddRange(children);
            }
            else
            {
                inflatedTypeCodeIds.Add(id);
            }
        }

        return inflatedTypeCodeIds;
    }
}
