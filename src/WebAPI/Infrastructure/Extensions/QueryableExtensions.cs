using System.Linq.Expressions;
using WebAPI.Infrastructure.Enums;

namespace WebAPI.Infrastructure.Extensions;

public static class QueryableExtensions
{
    public static IQueryable<T> OrderBySortOrder<T, TProp>(this IQueryable<T> queryable, Expression<Func<T, TProp>> expression, PropertySortType? sortOrder = PropertySortType.ASC)
    {
        return sortOrder is null or PropertySortType.ASC ? queryable.OrderBy(expression) : queryable.OrderByDescending(expression);
    }

    public static ParallelQuery<T> OrderBySortOrder<T, TProp>(this ParallelQuery<T> queryable, Func<T, TProp> expression, PropertySortType? sortOrder = PropertySortType.ASC)
    {
        return sortOrder is null or PropertySortType.ASC ? queryable.OrderBy(expression) : queryable.OrderByDescending(expression);
    }
}
