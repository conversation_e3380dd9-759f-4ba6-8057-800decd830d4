using Serilog.Configuration;
using Serilog.Core;
using Serilog.Events;

namespace WebAPI.Infrastructure.Extensions;

internal static class LoggerEnrichmentConfigurationExtensions
{
    public static LoggerConfiguration WithPodName(this LoggerEnrichmentConfiguration enrichmentConfiguration)
    {
        ArgumentNullException.ThrowIfNull(enrichmentConfiguration);

        return enrichmentConfiguration.With<LogNameEnricher>();
    }

    public static LoggerConfiguration WithApplicationName(this LoggerEnrichmentConfiguration enrichmentConfiguration)
    {
        ArgumentNullException.ThrowIfNull(enrichmentConfiguration);

        return enrichmentConfiguration.With<ApplicationNameEnricher>();
    }

    public static LoggerConfiguration WithApplicationVersion(this LoggerEnrichmentConfiguration enrichmentConfiguration)
    {
        ArgumentNullException.ThrowIfNull(enrichmentConfiguration);

        return enrichmentConfiguration.With<ApplicationVersionEnricher>();
    }
}

internal sealed class ApplicationNameEnricher : ILogEventEnricher
{
    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        var property = propertyFactory.CreateProperty("ApplicationName", "streams-apollo");
        logEvent.AddOrUpdateProperty(property);
    }
}

internal sealed class ApplicationVersionEnricher : ILogEventEnricher
{
    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        var applicationVersion = Environment.GetEnvironmentVariable("DH__APPLICATION__VERSION") ?? "1.0.0";

        var property = propertyFactory.CreateProperty("ApplicationVersion", applicationVersion);
        logEvent.AddOrUpdateProperty(property);
    }
}

internal sealed class LogNameEnricher : ILogEventEnricher
{
    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        var podName = Environment.GetEnvironmentVariable("POD_NAME");

        var property = propertyFactory.CreateProperty("PodName", podName);
        logEvent.AddOrUpdateProperty(property);
    }
}
