using SharedKernel.Entities;
using WebAPI.Features.Streams.Shared;

namespace WebAPI.Infrastructure.Extensions;

public static class StreamMetadataExtensions
{
    public static IEnumerable<Filter> GetFilters(this IEnumerable<StreamMetadataFilter> streamMetadataFilters,
        StreamMetadataFilterName filterName, bool includeFlag)
    {
        return streamMetadataFilters
            .Where(f => f.IncludeFlag == includeFlag &&
                        f.FilterName.Equals(filterName.ToString(), StringComparison.OrdinalIgnoreCase))
            .Select(x => new Filter(x.Id.Value, x.FilterValue));
    }
}
