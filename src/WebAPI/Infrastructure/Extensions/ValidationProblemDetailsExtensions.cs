using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace WebAPI.Infrastructure.Extensions;

public static class ValidationProblemDetailsExtensions
{
    public static ValidationProblemDetails ToProblemDetails(this IReadOnlyList<IError> errors)
    {
        var modelStateDictionary = new ModelStateDictionary();


        foreach (var error in errors)
        {
            modelStateDictionary.AddModelError(error.Reasons?.FirstOrDefault()?.Message ?? "Error", error.Message);
        }

        return new ValidationProblemDetails(modelStateDictionary)
        {
            Type = "https://tools.ietf.org/html/rfc9110#section-15.5.1",
        };
    }

}
