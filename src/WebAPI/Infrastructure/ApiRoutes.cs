namespace WebAPI.Infrastructure;

internal static class ApiRoute
{
    const string BaseApiRoute = "streams-api";
    public const string AiGeneratorRoute = $"{BaseApiRoute}/ai";
    public const string AiModelRoute = $"{BaseApiRoute}/ai-models";
    public const string OrganisationRoute = $"{BaseApiRoute}/organisations";
    public const string PeopleRoute = $"{BaseApiRoute}/people";
    public const string ProgrammeRoute = $"{BaseApiRoute}/programmes";
    public const string ThemeRoute = $"{BaseApiRoute}/themes";
    public const string StreamAlertRoute = $"{BaseApiRoute}/stream-alerts";
    public const string StreamRoute = $"{BaseApiRoute}/streams";
    public const string TypeCodeRoute = $"{BaseApiRoute}/type-codes";
    public const string TypeCodeUpdateRoute = $"{BaseApiRoute}/type-codes/type-code";
}
