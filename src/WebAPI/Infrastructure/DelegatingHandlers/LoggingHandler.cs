namespace WebAPI.Infrastructure.DelegatingHandlers;

public class LoggingHandler : DelegatingHandler
{
    private readonly ILogger<LoggingHandler> _logger;

    public LoggingHandler(ILogger<LoggingHandler> logger)
    {
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        if (request.Content is null)
        {
            return await base.SendAsync(request, cancellationToken);
        }

        var requestBody = await request.Content.ReadAsStringAsync(cancellationToken);
        _logger.LogInformation("Send external request body: {RequestBody}", requestBody);

        return await base.SendAsync(request, cancellationToken);
    }
}
