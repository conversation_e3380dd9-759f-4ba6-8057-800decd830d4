using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using OpenSearch.Client;
using SharedKernel.Configurations;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.UniqueAiMetadata;
using SharedKernel.Services.NotificationApi;
using SharedKernel.Services.UserApi;
using WebAPI.Features.StreamAlerts.HangfireApi;
using WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;
using WebAPI.Infrastructure.Behaviours;
using WebAPI.Infrastructure.Configurations;
using WebAPI.Infrastructure.DelegatingHandlers;
using WebAPI.Infrastructure.Static;
using WebAPI.Persistence;

namespace WebAPI.Infrastructure;

internal static class DependencyInjection
{
    private static IConfiguration _configuration;
    private static IWebHostEnvironment _environment;
    private static readonly MySqlServerVersion mySqlServerVersion = new(new Version(8, 0, 21));

    public static IServiceCollection AddDependencyInjection(
        this IServiceCollection services,
        IConfiguration config,
        IWebHostEnvironment environment)
    {
        _configuration = config;
        _environment = environment;

        services
            .AddControllers()
            .AddJsonOptions((options) =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });

        services.AddProblemDetails(o => o.CustomizeProblemDetails = context =>
        {
            var activity = context.HttpContext.Features.Get<IHttpActivityFeature>()?.Activity;

            context.ProblemDetails.Extensions.TryAdd("traceId", activity?.Id);
            context.ProblemDetails.Extensions.TryAdd("requestId", context.HttpContext.TraceIdentifier);
            context.ProblemDetails.Instance =
                $"{context.HttpContext.Request.Method} {context.HttpContext.Request.Path}";
        });

        services.AddAutoMapper(Assembly.GetExecutingAssembly());
        services.AddValidatorsFromAssemblyContaining<Program>(includeInternalTypes: true);
        services.AddFluentValidationAutoValidation();
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(Program).Assembly));

        services.AddCors();
        services.AddHttpContextAccessor();

        ConfigureAuthentication(services);
        ConfigureSwagger(services);
        ConfigureDbContext(config, services);
        ConfigureIoC(services);
        ConfigureHealthChecks(services);

        return services;
    }

    private static void ConfigureAuthentication(IServiceCollection services)
    {
        var jwtConfigurations = _configuration.GetSection(nameof(JwtConfigurations)).Get<JwtConfigurations>();

        services.AddAuthorization(options =>
        {
            options.FallbackPolicy = new AuthorizationPolicyBuilder()
                .RequireAuthenticatedUser()
                .RequireRole(InternalRole.GetRoles)
                .Build();
        }).AddAuthentication()
            .AddJwtBearer(options =>
            {
                options.SaveToken = true;
                options.RequireHttpsMetadata = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidAudience = jwtConfigurations.Audience,
                    ValidateIssuer = true,
                    ValidIssuer = jwtConfigurations.Issuer,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    RequireExpirationTime = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtConfigurations.Key))
                };

                options.Events = new JwtBearerEvents
                {
                    OnTokenValidated = context =>
                    {
                        var claimsPrincipal = context.Principal;
                        var isStaticToken = claimsPrincipal?.FindFirst("staticToken")?.Value == "true";

                        if (isStaticToken)
                        {
                            // Add a custom claim to bypass role checks
                            var identity = claimsPrincipal.Identity as ClaimsIdentity;
                            identity?.AddClaim(new Claim(ClaimTypes.Role, "ServiceUser"));

                            return Task.CompletedTask;
                        }

                        return Task.CompletedTask;
                    }
                };
            });
    }

    private static void ConfigureSwagger(IServiceCollection services)
    {
        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(swaggerGenOptions =>
        {
            swaggerGenOptions.SwaggerDoc("v1", new OpenApiInfo { Title = "StreamsAPI", Version = "v1" });
            swaggerGenOptions.EnableAnnotations();

            var securityScheme = new OpenApiSecurityScheme
            {
                In = ParameterLocation.Header,
                Description = "Please insert JWT token into field",
                Name = "Authorization",
                Type = SecuritySchemeType.Http,
                BearerFormat = "JWT",
                Scheme = "bearer",
                Reference = new OpenApiReference
                {
                    Id = JwtBearerDefaults.AuthenticationScheme,
                    Type = ReferenceType.SecurityScheme
                }
            };

            swaggerGenOptions.AddSecurityDefinition(securityScheme.Reference.Id, securityScheme);
            swaggerGenOptions.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {securityScheme, Array.Empty<string>()}
            });

            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);

            swaggerGenOptions.IncludeXmlComments(xmlPath);
            swaggerGenOptions.CustomSchemaIds(s => s.FullName);
        });
    }

    private static void ConfigureDbContext(IConfiguration config, IServiceCollection services)
    {
        var mysqlConfig = config.GetSection("MySQL").Get<MySqlConfigurationSettings>();
        var connectionString = $"Server={mysqlConfig?.HostName};" +
                               $"Port={mysqlConfig?.Port};" +
                               $"Database={mysqlConfig?.Database};" +
                               $"User={mysqlConfig?.UserName};Password={mysqlConfig?.Password};";

        services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseMySql(connectionString, mySqlServerVersion);
        });
        services.AddScoped<ApplicationDbContext>();
    }

    private static void ConfigureIoC(IServiceCollection services)
    {
        services.AddScoped(
            typeof(IPipelineBehavior<,>),
            typeof(ValidationPipelineBehaviour<,>));

        services.AddScoped<ITokenProvider, TokenService>();
        services.AddScoped<IDateTimeService, DateTimeService>();
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddScoped<IColibriApi, ColibriApiClient>();
        services.AddScoped<IHangfireApi, HangfireApiClient>();
        services.AddScoped<IUserApiClient, UserApiClient>();
        services.AddScoped<INotificationApiClient, NotificationApiClient>();
        services.AddTransient<LoggingHandler>();
        services.AddTransient<IAlertCalculator, AlertCalculator>();
        services.AddTransient<IUniqueAiMetadataConverter, UniqueAiMetadataConverter>();

        RegisterHttpClients(services);

        var openSearchSettings = _configuration
            .GetSection(nameof(Configurations.OpenSearch))
            .Get<Configurations.OpenSearch>();

        var openSearchClientSettings = new ConnectionSettings(new Uri($"{openSearchSettings.Protocol}://{openSearchSettings.HostName}"))
            .ThrowExceptions()
            .DisableDirectStreaming()
            .EnableDebugMode()
            .MaximumRetries(3)
            .DefaultIndex(openSearchSettings.DefaultIndex)
            .BasicAuthentication(openSearchSettings.UserName, openSearchSettings.Password);

        services.AddSingleton<IOpenSearchClient>(new OpenSearchClient(openSearchClientSettings));

        services.AddScoped<IOpenSearchService, OpenSearchService>();

        services.Configure<AirMonitoringApi>(
            _configuration.GetSection(
                key: nameof(AirMonitoringApi)));

        var cacheSettings = _configuration
            .GetSection(nameof(Caching))
            .Get<Caching>();

        if (cacheSettings.UseDistributedCache)
        {
            services.AddSingleton<ICacheService, DistributedCacheService>();
        }
        else
        {
            services.AddSingleton<ICacheService, MemoryCacheService>();
        }
    }

    private static void RegisterHttpClients(IServiceCollection services)
    {
        var colibriApiClientSettings = _configuration
            .GetSection(nameof(AirMonitoringApi))
            .Get<AirMonitoringApi>();

        services.AddHttpClient(ColibriApiClient.ClientName, client =>
                client.BaseAddress = new Uri(colibriApiClientSettings.Url))
            .AddHttpMessageHandler<LoggingHandler>()
            .AddStandardResilienceHandler(options =>
            {
                options.AttemptTimeout.Timeout =
                    TimeSpan.FromSeconds(60);
                options.TotalRequestTimeout.Timeout =
                    TimeSpan.FromSeconds(300);
                options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(120);
            }); // https://learn.microsoft.com/en-us/dotnet/core/resilience/http-resilience?tabs=dotnet-cli#standard-resilience-handler-defaults

        var hangfireApiClientSettings = _configuration
            .GetSection(nameof(HangfireApi))
            .Get<HangfireApi>();

        services.AddHttpClient(HangfireApiClient.ClientName, client =>
                client.BaseAddress = new Uri(hangfireApiClientSettings.Url))
            .AddHttpMessageHandler<LoggingHandler>()
            .AddStandardResilienceHandler()
            ;

        var userApiClientSettings = _configuration
           .GetSection(nameof(UserApi))
           .Get<UserApi>();

        services.AddHttpClient(UserApiClient.ClientName, client =>
             client.BaseAddress = new Uri(userApiClientSettings.Url))
           .AddStandardResilienceHandler() // https://learn.microsoft.com/en-us/dotnet/core/resilience/http-resilience?tabs=dotnet-cli#standard-resilience-handler-defaults
            ;

        var notificationApiClientSettings = _configuration
            .GetSection(nameof(NotificationApi))
            .Get<NotificationApi>();

        services.AddHttpClient(NotificationApiClient.ClientName, client =>
                client.BaseAddress = new Uri(notificationApiClientSettings.Url))
            .AddStandardResilienceHandler() // https://learn.microsoft.com/en-us/dotnet/core/resilience/http-resilience?tabs=dotnet-cli#standard-resilience-handler-defaults
            ;
    }

    private static void ConfigureHealthChecks(IServiceCollection services)
    {
        services.AddHealthChecks()
            // Add a health check for the database and tag it as "ready"
            .AddDbContextCheck<ApplicationDbContext>("ApolloDbContext", tags: ["ready"])
            // Add a simple liveness check (no dependencies, just a heartbeat)
            .AddCheck("Self", () => HealthCheckResult.Healthy(), tags: ["live"]);

        if (_environment.IsDevelopment())
        {
            Console.WriteLine();
        }

        var openSearchSettings = _configuration
            .GetSection(nameof(Configurations.OpenSearch))
            .Get<Configurations.OpenSearch>();

        services.AddHealthChecks().AddElasticsearch(setup => setup
            .UseServer($"{openSearchSettings.Protocol}://{openSearchSettings.HostName}")
            .UseBasicAuthentication(openSearchSettings.UserName,
                openSearchSettings.Password), "OpenSearch", tags: ["ready"]);
    }
}
