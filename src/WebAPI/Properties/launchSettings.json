{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "index.html", "applicationUrl": "http://localhost:5140", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "index.html", "applicationUrl": "https://localhost:7037;http://localhost:5140", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}