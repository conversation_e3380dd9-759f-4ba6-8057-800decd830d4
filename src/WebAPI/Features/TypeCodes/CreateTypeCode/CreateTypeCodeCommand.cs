using SharedKernel.Primitives.Interfaces;

namespace WebAPI.Features.TypeCodes.CreateTypeCode;

public sealed record CreateTypeCodeCommand : ICommand<CreateTypeCodeResponse>
{
    public string Name { get; set; }
    public int ParentId { get; set; } = 999;
}
internal sealed class CreateTypeCodeCommandValidator : AbstractValidator<CreateTypeCodeCommand>
{
    public CreateTypeCodeCommandValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
    }
}
