using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.TypeCodes.CreateTypeCode;

public class CreateTypeCodeEndpoint : EndpointBaseAsync
    .WithRequest<CreateTypeCodeCommand>
    .WithResult<ActionResult<CreateTypeCodeResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public CreateTypeCodeEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpPost(ApiRoute.TypeCodeUpdateRoute)]
    [SwaggerOperation(Tags = [nameof(TypeCodes)])]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(CreateTypeCodeResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<CreateTypeCodeResponse>> HandleAsync(CreateTypeCodeCommand request, CancellationToken cancellationToken = new())
    {
        var commandResult = await _sender.Send(request,
            cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Created($"{ApiRoute.TypeCodeUpdateRoute}/{commandResult.Value.Id}", _mapper.Map<CreateTypeCodeResponse>(commandResult.Value));
    }
}
