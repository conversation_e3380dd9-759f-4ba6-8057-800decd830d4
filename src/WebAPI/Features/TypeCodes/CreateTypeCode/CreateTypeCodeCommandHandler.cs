using SharedKernel.Primitives.Interfaces;
using WebAPI.Persistence;

namespace WebAPI.Features.TypeCodes.CreateTypeCode;

internal sealed class CreateTypeCodeCommandHandler : ICommandHandler<CreateTypeCodeCommand, CreateTypeCodeResponse>
{
    private readonly ApplicationDbContext _dbContext;

    public CreateTypeCodeCommandHandler(
        ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<CreateTypeCodeResponse>> <PERSON>le(CreateTypeCodeCommand request, CancellationToken cancellationToken)
    {
        var typeCode = TypeCode.Create(
            request.Name, request.ParentId);

        await _dbContext.TypeCodes.AddAsync(typeCode, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
        return new CreateTypeCodeResponse
        {
            Id = typeCode.Id,
            Name = typeCode.Name,
            ParentId = typeCode.ParentId
        };
    }
}
