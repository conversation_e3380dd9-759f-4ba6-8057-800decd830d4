using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.TypeCodes.GetTypeCodes;

public class GetTypeCodesEndpoint : EndpointBaseAsync
    .WithRequest<GetTypeCodesFilterRequest>
    .WithResult<ActionResult<PaginatedQueryResult<GetTypeCodesResponse>>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetTypeCodesEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpGet(ApiRoute.TypeCodeRoute)]
    [SwaggerOperation(Tags = [nameof(TypeCodes)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PaginatedQueryResult<GetTypeCodesResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<PaginatedQueryResult<GetTypeCodesResponse>>> HandleAsync(
        [FromQuery] GetTypeCodesFilterRequest request,
        CancellationToken cancellationToken = new())
    {
        var queryResult = await _sender.Send(new GetTypeCodesQuery(request.SearchKeyword, request.ParentId), cancellationToken);

        return Ok(_mapper.Map<IEnumerable<GetTypeCodesResponse>>(queryResult.Value));
    }
}

