using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Persistence;

namespace WebAPI.Features.TypeCodes.GetTypeCodes;

internal sealed class GetTypeCodesQueryHandler : IQueryHandler<GetTypeCodesQuery, PaginatedQueryResult<TypeCode>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetTypeCodesQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<PaginatedQueryResult<TypeCode>>> Handle(GetTypeCodesQuery request, CancellationToken cancellationToken)
    {
        IQueryable<TypeCode> query = _dbContext.TypeCodes;

        if (!string.IsNullOrWhiteSpace(request.SearchKeyword))
        {
            query = ApplyFiltering(query, request.SearchKeyword);
        }
        if (request.ParentId.HasValue)
        {
            query = query.Where(q => q.ParentId == request.ParentId);
        }

        return await PaginatedQueryResult<TypeCode>.CreateAsync(query, 1, Int32.MaxValue, cancellationToken);
    }

    private static IQueryable<TypeCode> ApplyFiltering(IQueryable<TypeCode> query, string searchKeyword)
    {
        var searchTerms = searchKeyword.Split([' '], StringSplitOptions.RemoveEmptyEntries);

        return searchTerms.Aggregate(query,
            (current, term) =>
                current.Where(u =>
                    EF.Functions.Like(u.Name, $"%{term}%")));
    }
}
