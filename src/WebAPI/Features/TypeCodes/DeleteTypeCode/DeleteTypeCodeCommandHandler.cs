using SharedKernel.Primitives.Interfaces;
using WebAPI.Persistence;

namespace WebAPI.Features.TypeCodes.DeleteTypeCode;

internal sealed class DeleteTypeCodeCommandHandler : ICommandHandler<DeleteTypeCodeCommand>
{
    private readonly ApplicationDbContext _dbContext;

    public DeleteTypeCodeCommandHandler(
        ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result> <PERSON><PERSON>(DeleteTypeCodeCommand request, CancellationToken cancellationToken)
    {
        var typeCode = await _dbContext.TypeCodes.FindAsync([request.Id], cancellationToken);

        if (typeCode is null)
        {
            return Result.Fail(new Error($"The Typecode  with {request.Id} was not found", new Error("TypeCodeId")));
        }
        var typeCodes = _dbContext.TypeCodes.Where(t => t.ParentId == request.Id);
        _dbContext.TypeCodes.RemoveRange(typeCodes);
        _dbContext.TypeCodes.Remove(typeCode);
        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Ok();
    }
}
