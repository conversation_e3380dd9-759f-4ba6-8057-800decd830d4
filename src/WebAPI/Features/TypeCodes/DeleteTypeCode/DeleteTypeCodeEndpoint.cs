using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.TypeCodes.DeleteTypeCode;

public class DeleteTypeCodeEndpoint : EndpointBaseAsync
    .WithRequest<DeleteTypeCodeCommand>
    .WithoutResult
{
    private readonly ISender _sender;

    public DeleteTypeCodeEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpDelete($"{ApiRoute.TypeCodeUpdateRoute}/{{id}}")]
    [SwaggerOperation(Tags = [nameof(TypeCodes)])]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult> HandleAsync(DeleteTypeCodeCommand request, CancellationToken cancellationToken = new())
    {
        var commandResult = await _sender.Send(request,
            cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Ok();
    }
}
