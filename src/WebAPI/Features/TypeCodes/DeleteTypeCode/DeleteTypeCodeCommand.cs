using SharedKernel.Primitives.Interfaces;

namespace WebAPI.Features.TypeCodes.DeleteTypeCode;

public sealed record DeleteTypeCodeCommand : ICommand
{
    [FromRoute(Name = "id")]
    public int Id { get; set; }
}
internal sealed class DeleteTypeCodeCommandValidator : AbstractValidator<DeleteTypeCodeCommand>
{
    public DeleteTypeCodeCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}
