using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.TypeCodes.GetTypeCode;

[Route(ApiRoute.TypeCodeRoute)]
public class GetTypeCodeEndpoint : EndpointBaseAsync
    .WithRequest<int>
    .WithResult<ActionResult<GetTypeCodeResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetTypeCodeEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }


    [HttpGet("{id:int}")]
    [SwaggerOperation(Tags = [nameof(TypeCodes)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(GetTypeCodeResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<GetTypeCodeResponse>> HandleAsync(
        [FromRoute] int id,
        CancellationToken cancellationToken = new())
    {
        var queryResult = await _sender.Send(new GetTypeCodeQuery(id), cancellationToken);

        if (queryResult.IsSuccess)
        {
            return Ok(_mapper.Map<GetTypeCodeResponse>(queryResult.Value));
        }

        if (queryResult.Errors.FirstOrDefault()?.Reasons.FirstOrDefault()?.Message == "Type Code Id Not Found")
        {
            return NotFound();
        }

        return BadRequest(queryResult.Errors.ToProblemDetails());
    }
}

