using SharedKernel.Primitives.Interfaces;
using WebAPI.Persistence;

namespace WebAPI.Features.TypeCodes.GetTypeCode;

internal sealed class GetTypeCodeQueryHandler : IQueryHandler<GetTypeCodeQuery, TypeCode>
{
    private readonly ApplicationDbContext _dbContext;

    public GetTypeCodeQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<TypeCode>> <PERSON>le(GetTypeCodeQuery request, CancellationToken cancellationToken)
    {
        var typeCode = await _dbContext.TypeCodes.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (typeCode is null)
        {
            return Result.Fail(new Error($"The type code with id {request.Id} was not found",
                new Error("Type Code Id Not Found")));
        }

        return Result.Ok(typeCode);
    }
}
