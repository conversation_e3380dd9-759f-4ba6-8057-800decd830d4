using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.TypeCodes.GetTypeCodesById;

public class GetTypeCodesByIdsEndpoint : EndpointBaseAsync
    .WithRequest<GetTypeCodesByIdsQuery>
    .WithResult<ActionResult<IList<GetTypeCodesByIdsResponse>>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetTypeCodesByIdsEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpPost(ApiRoute.TypeCodeRoute)]
    [SwaggerOperation(Tags = [nameof(TypeCodes)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IList<GetTypeCodesByIdsResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<IList<GetTypeCodesByIdsResponse>>> HandleAsync(
        [FromBody] GetTypeCodesByIdsQuery request,
        CancellationToken cancellationToken = new())
    {
        var queryResult = await _sender.Send(new GetTypeCodesByIdsQuery(request.Ids), cancellationToken);

        return Ok(_mapper.Map<IEnumerable<GetTypeCodesByIdsResponse>>(queryResult.Value));
    }
}
