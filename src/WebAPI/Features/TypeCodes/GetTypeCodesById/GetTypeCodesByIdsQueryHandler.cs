using SharedKernel.Primitives.Interfaces;
using WebAPI.Persistence;

namespace WebAPI.Features.TypeCodes.GetTypeCodesById;

internal sealed class GetTypeCodesByIdsQueryHandler : IQueryHandler<GetTypeCodesByIdsQuery, IList<TypeCode>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetTypeCodesByIdsQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<IList<TypeCode>>> Handle(GetTypeCodesByIdsQuery request,
        CancellationToken cancellationToken)
    {
        return await _dbContext.TypeCodes
            .AsNoTracking()
            .Where(u => request.Ids.Contains(u.Id))
            .ToListAsync(cancellationToken);
    }
}
