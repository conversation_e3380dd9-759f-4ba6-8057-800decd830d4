using SharedKernel.Primitives.Interfaces;

namespace WebAPI.Features.TypeCodes.UpdateTypeCode;

public sealed record UpdateTypeCodeCommand : ICommand<UpdateTypeCodeResponse>
{
    [FromRoute(Name = "id")]
    public int Id { get; set; }
    [FromBody]
    public UpdateTypeCodeRequest Request { get; set; }
}
public class UpdateTypeCodeRequest
{
    public string Name { get; set; }
    public int ParentId { get; set; } = 999;
}
internal sealed class UpdateTypeCodeCommandValidator : AbstractValidator<UpdateTypeCodeCommand>
{
    public UpdateTypeCodeCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Request).NotNull();
        RuleFor(x => x.Request.Name).NotEmpty();
    }
}
