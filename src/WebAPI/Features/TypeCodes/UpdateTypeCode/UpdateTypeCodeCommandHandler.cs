using SharedKernel.Primitives.Interfaces;
using WebAPI.Persistence;

namespace WebAPI.Features.TypeCodes.UpdateTypeCode;

internal sealed class UpdateTypeCodeCommandHandler : ICommandHandler<UpdateTypeCodeCommand, UpdateTypeCodeResponse>
{
    private readonly ApplicationDbContext _dbContext;

    public UpdateTypeCodeCommandHandler(
        ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<UpdateTypeCodeResponse>> <PERSON><PERSON>(UpdateTypeCodeCommand request, CancellationToken cancellationToken)
    {
        var typeCode = await _dbContext.TypeCodes.FindAsync([request.Id], cancellationToken);
        if (typeCode is null)
        {
            return Result.Fail(new Error($"The Typecode  with {request.Id} was not found", new Error("TypeCodeId")));
        }
        typeCode.Update(request.Request.Name, request.Request.ParentId);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return new UpdateTypeCodeResponse
        {
            Id = typeCode.Id,
            Name = typeCode.Name,
            ParentId = typeCode.ParentId
        };
    }
}
