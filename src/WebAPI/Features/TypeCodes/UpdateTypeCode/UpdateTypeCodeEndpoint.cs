using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.TypeCodes.UpdateTypeCode;

public class UpdateTypeCodeEndpoint : EndpointBaseAsync
    .WithRequest<UpdateTypeCodeCommand>
    .WithResult<ActionResult<UpdateTypeCodeResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public UpdateTypeCodeEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpPut($"{ApiRoute.TypeCodeUpdateRoute}/{{id}}")]
    [SwaggerOperation(Tags = [nameof(TypeCodes)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(UpdateTypeCodeResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<UpdateTypeCodeResponse>> HandleAsync(UpdateTypeCodeCommand request, CancellationToken cancellationToken = new())
    {
        var commandResult = await _sender.Send(request,
            cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Ok(_mapper.Map<UpdateTypeCodeResponse>(commandResult.Value));
    }
}
