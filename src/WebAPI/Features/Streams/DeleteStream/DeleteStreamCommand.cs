using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Features.StreamAlerts.HangfireApi;
using WebAPI.Persistence;

namespace WebAPI.Features.Streams.DeleteStream;

public sealed record DeleteStreamRequest(Guid Id);
internal sealed class DeleteStreamRequestValidator : AbstractValidator<DeleteStreamRequest>
{
    public DeleteStreamRequestValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public sealed record DeleteStreamCommand(StreamId Id) : ICommand<Response>;

public sealed record Response(StreamId Id);

internal sealed class DeleteStreamCommandValidator : AbstractValidator<DeleteStreamCommand>
{
    public DeleteStreamCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

internal sealed class DeleteStreamCommandHandler : ICommandHandler<DeleteStreamCommand, Response>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IHangfireApi _hangfireApi;

    public DeleteStreamCommandHandler(ApplicationDbContext dbContext,
        IHangfireApi hangfireApi)
    {
        _dbContext = dbContext;
        _hangfireApi = hangfireApi;
    }

    public async Task<Result<Response>> Handle(DeleteStreamCommand request, CancellationToken cancellationToken)
    {
        var stream = await _dbContext.Streams.FindAsync([request.Id], cancellationToken);

        if (stream is null)
        {
            return Result.Fail<Response>($"Stream with ID {request.Id} not found.");
        }

        var amplifiers = _dbContext.Amplifier.Where(a => a.StreamId == request.Id);
        var fewShots = _dbContext.FewShot.Where(f => f.StreamId == request.Id);
        var streamMetadataFilters = _dbContext.StreamMetadataFilter.Where(sf => sf.StreamId == request.Id);
        var streamFilterTypeCodes = _dbContext.StreamFilterTypeCode.Where(sf => sf.StreamId == request.Id);
        var streamAlerts = _dbContext.StreamAlerts.Where(s => s.StreamId == request.Id && !s.IsDeleted);

        if (amplifiers is not null)
        {
            _dbContext.Amplifier.RemoveRange(amplifiers);
        }

        if (fewShots is not null)
        {
            _dbContext.FewShot.RemoveRange(fewShots);
        }

        if (streamMetadataFilters is not null)
        {
            _dbContext.StreamMetadataFilter.RemoveRange(streamMetadataFilters);
        }

        if (streamFilterTypeCodes is not null)
        {
            _dbContext.StreamFilterTypeCode.RemoveRange(streamFilterTypeCodes);
        }
        //Remove all associated stream alerts and hangfire jobs.
        if (streamAlerts is not null)
        {
            _dbContext.StreamAlerts.RemoveRange(streamAlerts);
            foreach (var item in streamAlerts)
            {
                var hangfireApiResult = await _hangfireApi.DeleteScheduleStreamAlertJob(item.HangfireId, cancellationToken);

                if (hangfireApiResult.IsFailed)
                {
                    return Result.Fail(hangfireApiResult.Errors);
                }
            }
        }

        _dbContext.Streams.Remove(stream);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return new Response(request.Id);
    }

}
