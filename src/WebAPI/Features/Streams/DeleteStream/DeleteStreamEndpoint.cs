using SharedKernel.Entities;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.Streams.DeleteStream;

[Route(ApiRoute.StreamRoute)]
public class DeleteStreamEndpoint : EndpointBaseAsync.WithRequest<Guid>
    .WithResult<ActionResult<Response>>
{
    private readonly ISender _sender;

    public DeleteStreamEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpDelete("{id}")]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status204NoContent, Type = typeof(Response))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<Response>> HandleAsync([FromRoute] Guid id, CancellationToken cancellationToken = default)
    {
        var commandResult = await _sender.Send(new DeleteStreamCommand(new StreamId(id)));

        if (commandResult.IsFailed)
        {
            return NotFound(commandResult.Errors.ToProblemDetails());
        }

        return NoContent();
    }
}
