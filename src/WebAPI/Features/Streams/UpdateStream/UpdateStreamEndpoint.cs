using System.Net;
using SharedKernel.Entities;
using WebAPI.Features.Streams.GetIsMentionStream;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;
using Response = WebAPI.Features.Streams.Shared.Response;

namespace WebAPI.Features.Streams.UpdateStream;

[Route(ApiRoute.StreamRoute)]
public class UpdateStreamEndpoint : EndpointBaseAsync
    .WithRequest<UpdateStreamRequest>
    .WithResult<ActionResult<Response>>
{
    private readonly ISender _sender;

    public UpdateStreamEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpPut("{id}")]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<Response>> HandleAsync([FromRoute] UpdateStreamRequest updateStreamRequest,
        CancellationToken cancellationToken = default)
    {
        var existingStream = await _sender.Send(new GetIsMentionStreamQuery(new StreamId(updateStreamRequest.Id)),
            cancellationToken);

        if (existingStream.IsFailed)
        {
            return NotFound(existingStream.Errors.ToProblemDetails());
        }

        var commandResult =
            await _sender.Send(
                new UpdateStreamCommand(new StreamId(updateStreamRequest.Id), updateStreamRequest.Request, existingStream.Value.IsMention),
                cancellationToken);

        if (commandResult.IsFailed)
        {
            var httpStatus = commandResult.Errors.FirstOrDefault()
                ?.Metadata?.FirstOrDefault().Value;

            if (httpStatus != null && Enum.TryParse(typeof(HttpStatusCode), httpStatus.ToString(), out var statusCode))
            {
                return (HttpStatusCode)statusCode switch
                {
                    HttpStatusCode.BadRequest => Problem(
                        statusCode: StatusCodes.Status400BadRequest,
                        detail: commandResult.Errors.FirstOrDefault().Message),

                    HttpStatusCode.Unauthorized => Problem(
                        statusCode: StatusCodes.Status401Unauthorized,
                        detail: commandResult.Errors.FirstOrDefault().Message),

                    HttpStatusCode.Forbidden => Problem(
                        statusCode: StatusCodes.Status403Forbidden,
                        detail: commandResult.Errors.FirstOrDefault().Message),

                    HttpStatusCode.NotFound => Problem(
                        statusCode: StatusCodes.Status404NotFound,
                        detail: commandResult.Errors.FirstOrDefault().Message),

                    _ => Problem(commandResult.Errors.FirstOrDefault().Message)
                };
            }

            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Ok(commandResult.Value);
    }
}
