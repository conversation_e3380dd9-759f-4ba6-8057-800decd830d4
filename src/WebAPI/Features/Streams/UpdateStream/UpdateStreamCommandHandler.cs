using System.Net;
using SharedKernel.Entities;
using SharedKernel.Primitives.Errors;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.AddAmplifiers;
using SharedKernel.Services.ColibriApi.AddFewShots;
using SharedKernel.Services.UserApi;
using WebAPI.Features.Streams.Shared;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Persistence;

namespace WebAPI.Features.Streams.UpdateStream;

internal sealed class UpdateStreamCommandHandler : ICommandHandler<UpdateStreamCommand, Response>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IColibriApi _colibriApi;
    private readonly IUserApiClient _userClient;
    private readonly ILogger<UpdateStreamCommandHandler> _logger;

    public UpdateStreamCommandHandler(
        ApplicationDbContext dbContext,
        IColibriApi colibriApi,
        IUserApiClient userClient,
        ILogger<UpdateStreamCommandHandler> logger)
    {
        _dbContext = dbContext;
        _colibriApi = colibriApi;
        _userClient = userClient;
        _logger = logger;
    }

    public async Task<Result<Response>> Handle(UpdateStreamCommand updateStreamCommand,
        CancellationToken cancellationToken)
    {
        var stream = await _dbContext.Streams.FindAsync([updateStreamCommand.Id], cancellationToken);

        if (stream is null)
        {
            return Result.Fail(new Error($"The stream with id {updateStreamCommand.Id} was not found",
                new Error("StreamId")).WithMetadata(ErrorMetadataType.NotFound, HttpStatusCode.NotFound));
        }

        var companyNameResult = await GetCompanyName(updateStreamCommand.Request.CompanyId, cancellationToken);
        if (companyNameResult.IsFailed)
        {
            return Result.Fail(companyNameResult.Errors);
        }

        var companyName = companyNameResult.Value;

        UpdateStream(stream, updateStreamCommand, companyName);

        DeleteExistingEntries(updateStreamCommand.Id);
        var addNewEntriesResult = await AddNewEntries(stream, updateStreamCommand, cancellationToken);
        if (addNewEntriesResult.IsFailed)
        {
            return Result.Fail(addNewEntriesResult.Errors);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        return new Response(
            stream.Id.Value,
            updateStreamCommand.Request.Name,
            updateStreamCommand.Request.CompanyId,
            updateStreamCommand.Request.Prompt,
            updateStreamCommand.Request.AiGeneratedPrompt,
            stream.IsMention,
            updateStreamCommand.Request.Filters,
            updateStreamCommand.Request.ExclusionFilters,
            updateStreamCommand.Request.AiParameters,
            updateStreamCommand.Request.AdvancedSettings
        );
    }

    #region Private Helper Methods

    private async Task<Result<string>> GetCompanyName(Guid? companyId, CancellationToken cancellationToken)
    {
        if (!companyId.HasValue)
        {
            return Result.Ok<string>(null);
        }

        var userResponse = await _userClient.GetCompanyName(companyId.Value, cancellationToken);

        return userResponse.IsFailed ? Result.Fail(userResponse.Errors) : userResponse.Value.Name;
    }

    private static void UpdateStream(Stream stream, UpdateStreamCommand updateStreamCommand, string companyName)
    {
        if (updateStreamCommand.IsMention)
        {
            stream.UpdateMention(updateStreamCommand.Request.Name, updateStreamCommand.Request.CompanyId,
                companyName, null);
        }
        else
        {
            stream.Update(
                updateStreamCommand.Request.CompanyId,
                new AiModelId(updateStreamCommand.Request.AiParameters.AiModelIds.FirstOrDefault()),
                updateStreamCommand.Request.Name,
                updateStreamCommand.Request.Prompt,
                companyName,
                null,
                updateStreamCommand.Request.AiGeneratedPrompt,
                updateStreamCommand.Request.AiGeneratedPromptId,
                updateStreamCommand.Request.AiParameters.Confidence
            );
        }
    }

    private void DeleteExistingEntries(StreamId streamId)
    {
        var amplifiers = _dbContext.Amplifier.Where(a => a.StreamId == streamId);
        var fewShots = _dbContext.FewShot.Where(f => f.StreamId == streamId);
        var streamMetadataFilters = _dbContext.StreamMetadataFilter.Where(sf => sf.StreamId == streamId);
        var streamFilterTypeCodes = _dbContext.StreamFilterTypeCode.Where(sf => sf.StreamId == streamId);

        _dbContext.Amplifier.RemoveRange(amplifiers);

        _dbContext.FewShot.RemoveRange(fewShots);

        _dbContext.StreamMetadataFilter.RemoveRange(streamMetadataFilters);

        _dbContext.StreamFilterTypeCode.RemoveRange(streamFilterTypeCodes);
    }

    private async Task<Result> AddNewEntries(Stream stream, UpdateStreamCommand updateStreamCommand,
        CancellationToken cancellationToken)
    {
        var amplifiers = CreateAmplifiers(updateStreamCommand, stream.Id);
        var fewShots = CreateFewShots(updateStreamCommand, stream.Id);
        var streamMetadataFilters = CreateMetadataStreamFilters(updateStreamCommand, stream.Id);
        var streamFilterTypeCodes =
            await CreateStreamFilterTypeCodes(updateStreamCommand, stream.Id, cancellationToken);

        await AddEntitiesToContextAsync(_dbContext.StreamMetadataFilter, streamMetadataFilters, cancellationToken);
        await AddEntitiesToContextAsync(_dbContext.StreamFilterTypeCode, streamFilterTypeCodes, cancellationToken);

        if (amplifiers.Count > 0)
        {
            var handleAddAmplifiers = await HandleAddAmplifiers(amplifiers, cancellationToken);
            if (handleAddAmplifiers.IsFailed)
            {
                return Result.Fail(handleAddAmplifiers.Errors);
            }

            await _dbContext.Amplifier.AddRangeAsync(amplifiers, cancellationToken);
        }

        if (fewShots.Count > 0)
        {
            var handleAddFewShots = await HandleAddFewShots(fewShots, cancellationToken);
            if (handleAddFewShots.IsFailed)
            {
                return Result.Fail(handleAddFewShots.Errors);
            }

            await _dbContext.FewShot.AddRangeAsync(fewShots, cancellationToken);
        }

        return Result.Ok();
    }

    private static List<Amplifier> CreateAmplifiers(UpdateStreamCommand updateStreamCommand, StreamId streamId)
    {
        return updateStreamCommand.Request?.AdvancedSettings?.Amplifiers
            .Select(name => Amplifier.Create(streamId, name.ToLowerInvariant(),
                updateStreamCommand.Request.AdvancedSettings.AmplifierRelevancy.Value))
            .ToList() ?? [];
    }

    private static List<FewShot> CreateFewShots(UpdateStreamCommand updateStreamCommand, StreamId streamId)
    {
        return updateStreamCommand.Request?.AdvancedSettings?.FewShotItemIds
            .Select(itemId => FewShot.Create(streamId, itemId.Value,
                updateStreamCommand.Request.AdvancedSettings.FewShotRelevancy.Value))
            .ToList() ?? [];
    }

    private static List<StreamMetadataFilter> CreateMetadataStreamFilters(UpdateStreamCommand updateStreamCommand,
        StreamId streamId)
    {
        var streamFilters = new List<StreamMetadataFilter>();

        AddFiltersToList(streamFilters, updateStreamCommand.Request.Filters.People, streamId,
            StreamMetadataFilterName.People, true);
        AddFiltersToList(streamFilters, updateStreamCommand.Request.Filters.Organisations, streamId,
            StreamMetadataFilterName.Organisation, true);
        AddFiltersToList(streamFilters, updateStreamCommand.Request.Filters.Programmes, streamId,
            StreamMetadataFilterName.Programme, true);
        AddFiltersToList(streamFilters, updateStreamCommand.Request.Filters.Themes, streamId,
            StreamMetadataFilterName.Theme, true);
        AddFiltersToList(streamFilters, updateStreamCommand.Request.ExclusionFilters.People, streamId,
            StreamMetadataFilterName.People, false);
        AddFiltersToList(streamFilters, updateStreamCommand.Request.ExclusionFilters.Organisations, streamId,
            StreamMetadataFilterName.Organisation, false);
        AddFiltersToList(streamFilters, updateStreamCommand.Request.ExclusionFilters.Programmes, streamId,
            StreamMetadataFilterName.Programme, false);
        AddFiltersToList(streamFilters, updateStreamCommand.Request.ExclusionFilters.Themes, streamId,
            StreamMetadataFilterName.Theme, false);

        return streamFilters;
    }

    private static void AddFiltersToList(List<StreamMetadataFilter> streamFilters, IEnumerable<Filter> filterValues,
        StreamId streamId, StreamMetadataFilterName filterName, bool include)
    {
        if (filterValues is null)
        {
            return;
        }

        foreach (var filter in filterValues)
        {
            streamFilters.Add(StreamMetadataFilter.Create(streamId, filterName, include, filter.Name));
        }
    }

    private async Task<List<StreamFilterTypeCode>> CreateStreamFilterTypeCodes(UpdateStreamCommand updateStreamCommand,
        StreamId streamId, CancellationToken cancellationToken)
    {
        var streamFilterTypeCodes = new List<StreamFilterTypeCode>();

        var inflatedTypeCodeIdFilters =
            await _dbContext.GetInflatedTypeCodesAsync(updateStreamCommand.Request.Filters.TypeCodeIds,
                cancellationToken);

        streamFilterTypeCodes.AddRange(
            inflatedTypeCodeIdFilters
                .Select(filter => StreamFilterTypeCode.Create(filter, streamId, true))
        );

        var inflatedTypeCodeIdExclusionFilters =
            await _dbContext.GetInflatedTypeCodesAsync(updateStreamCommand.Request.ExclusionFilters.TypeCodeIds,
                cancellationToken);

        streamFilterTypeCodes.AddRange(
            inflatedTypeCodeIdExclusionFilters.Select(filter =>
                StreamFilterTypeCode.Create(filter, streamId, false))
        );

        return streamFilterTypeCodes;
    }

    private static async Task AddEntitiesToContextAsync<T>(DbSet<T> dbSet, List<T> entities,
        CancellationToken cancellationToken) where T : class
    {
        if (entities.Count > 0)
        {
            await dbSet.AddRangeAsync(entities, cancellationToken);
        }
    }


    private async Task<Result> HandleAddAmplifiers(IReadOnlyList<Amplifier> amplifiers,
        CancellationToken cancellationToken)
    {
        var amplifiersTerms = amplifiers.Select(x => x.Name).ToList();
        return await HandleColibriApiRequest(
            () => _colibriApi.AddAmplifiers(new AddAmplifiersRequest(amplifiersTerms), cancellationToken),
            nameof(HandleAddAmplifiers),
            "amplifiers",
            response => response.ErrorAmplifiers
        );
    }

    private async Task<Result> HandleAddFewShots(IReadOnlyList<FewShot> fewShots, CancellationToken cancellationToken)
    {
        var fewShotsIds = fewShots.Select(x => x.ItemId).ToList();
        return await HandleColibriApiRequest(
            () => _colibriApi.AddFewShots(new AddFewShotsRequest(fewShotsIds), cancellationToken),
            nameof(HandleAddFewShots),
            "fewshots",
            response => response.ErrorFewShots
        );
    }

    private async Task<Result> HandleColibriApiRequest<TResponse, TError>(
        Func<Task<Result<TResponse>>> apiCall,
        string methodName,
        string entityName,
        Func<TResponse, IReadOnlyList<TError>> errorExtractor)
    {
        var colibriApiResult = await apiCall();

        if (colibriApiResult.IsFailed)
        {
            _logger.LogError("Failed to access {MethodName} from the Colibri API service", methodName);
            return Result.Fail(colibriApiResult.Errors);
        }

        var colibriResponseValue = colibriApiResult.Value;
        var errors = errorExtractor(colibriResponseValue);

        if (errors?.Count > 0)
        {
            using (_logger.BeginScope("{@Errors}", errors))
            {
                _logger.LogError("Failed to generate {EntityName} with Colibri API service", entityName);
            }

            return Result.Fail(new Error($"Failed to generate {entityName} from Colibri API service",
                new Error($"{entityName}Term")));
        }

        return Result.Ok();
    }

    #endregion
}
