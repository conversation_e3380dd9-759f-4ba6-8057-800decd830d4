using WebAPI.Infrastructure.Mappers;

namespace WebAPI.Features.Streams.GetFeedData;

public sealed record GetFeedDataResponse : IMapFrom<FeedDataItem>
{
    public int Id { get; init; }
    public string Title { get; init; }
    public DateTimeOffset Date { get; init; }
    public string TypecodeName { get; init; }
    public string SourceLink { get; init; }
    public List<string> People { get; init; } = new();
    public List<string> Theme { get; init; } = new();
    public List<string> Organisations { get; init; } = new();
    public List<string> Programmes { get; init; } = new();
}
