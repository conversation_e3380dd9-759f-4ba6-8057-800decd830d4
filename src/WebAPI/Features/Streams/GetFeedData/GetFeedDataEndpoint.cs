using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.Streams.GetFeedData;

[Route(ApiRoute.StreamRoute)]
public class GetFeedDataEndpoint : EndpointBaseAsync
    .WithoutRequest
    .WithResult<ActionResult<IEnumerable<GetFeedDataResponse>>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetFeedDataEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpGet("feed-data")]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IEnumerable<GetFeedDataResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<IEnumerable<GetFeedDataResponse>>> HandleAsync(CancellationToken cancellationToken = default)
    {
        var query = new GetFeedDataQuery();

        var commandResult = await _sender.Send(query, cancellationToken);

        if (commandResult.IsFailed)
        {
            return Problem(statusCode: StatusCodes.Status404NotFound, detail: commandResult.Errors[0].Message);
        }

        return Ok(_mapper.Map<IEnumerable<GetFeedDataResponse>>(commandResult.Value));
    }
}
