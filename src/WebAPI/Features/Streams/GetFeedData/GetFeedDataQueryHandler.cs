using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.GetTopNews;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Infrastructure.Static;
using WebAPI.Persistence;
using FluentResults;

namespace WebAPI.Features.Streams.GetFeedData;

public record FeedDataItem(int Id, string Title, string SourceLink, string TypecodeName, DateTimeOffset Date)
{
    public List<string> People { get; init; } = new();
    public List<string> Theme { get; init; } = new();
    public List<string> Organisations { get; init; } = new();
    public List<string> Programmes { get; init; } = new();
};

internal sealed class GetFeedDataQueryHandler : IQueryHandler<GetFeedDataQuery, IEnumerable<FeedDataItem>>
{
    private const double DefaultWeightValue = 0.25D;

    private readonly IColibriApi _colibriApi;
    private readonly ILogger<GetFeedDataQueryHandler> _logger;
    private readonly ApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;

    public GetFeedDataQueryHandler(
        IColibriApi colibriApi,
        ILogger<GetFeedDataQueryHandler> logger,
        ApplicationDbContext dbContext,
        ICurrentUserService currentUserService)
    {
        _colibriApi = colibriApi;
        _logger = logger;
        _dbContext = dbContext;
        _currentUserService = currentUserService;
    }

    public async Task<Result<IEnumerable<FeedDataItem>>> Handle(GetFeedDataQuery request, CancellationToken cancellationToken)
    {
        var userId = _currentUserService.Id;

        if (string.IsNullOrEmpty(userId))
        {
            return Result.Fail(new Error("User ID not found in JWT token", new Error("UserId")));
        }

        // Find the user's feed stream
        var stream = await _dbContext
            .Streams
            .Include(s => s.StreamFilterTypeCodes)
            .ThenInclude(sft => sft.TypeCode)
            .Include(s => s.Amplifiers)
            .Include(stream => stream.FewShots)
            .Include(s => s.StreamMetadataFilters)
            .AsNoTracking()
            .SingleOrDefaultAsync(s => s.IsFeed && s.CreatedBy == userId, cancellationToken);

        if (stream is null)
        {
            return Result.Fail(new Error($"Feed stream not found for user {userId}", new Error("FeedStream")));
        }

        var typeCodeFilters = GetTypeCodeFilters(stream.StreamFilterTypeCodes);
        var streamAmplifiersFilters = GetAmplifiersFilters(stream.Amplifiers);
        var streamFewshotFilters = GetFewshotFilters(stream.FewShots);
        var peopleFilters = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.People, true)
            .Select(x => x.Name).ToList();
        var peopleExclusions = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.People, false)
            .Select(x => x.Name).ToList();
        var organisationFilters = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Organisation, true)
            .Select(x => x.Name).ToList();
        var organisationExclusions = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Organisation, false)
            .Select(x => x.Name).ToList();
        var programmeFilters = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Programme, true)
            .Select(x => x.Name).ToList();
        var programmeExclusions = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Programme, false)
            .Select(x => x.Name).ToList();
        var themeFilters = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Theme, true)
            .Select(x => x.Name).ToList();
        var themeExclusions = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Theme, false)
            .Select(x => x.Name).ToList();

        GetTopNewsRequest getTopNewsRequest;
        if (stream.AiGeneratedPromptId is > 0)
        {
            getTopNewsRequest = new GetTopNewsRequest(
                clientId: DefaultDehavillandConfigurations.DehavillandCompanyId,
                clientName: DefaultDehavillandConfigurations.DehavillandCompanyName,
                queryId: stream.AiGeneratedPromptId.Value)
            {
                TypeCodesToInclude = typeCodeFilters.TypeCodesToInclude,
                TypeCodesToExclude = typeCodeFilters.TypeCodesToExclude,
                FewshotsIds = streamFewshotFilters.FewshotIds,
                FewshotsWeight = streamFewshotFilters.FewshotWeight,
                AmplifiersTerms = streamAmplifiersFilters.AmplifiersTerms,
                AmplifiersWeight = streamAmplifiersFilters.AmplifiersWeight,
                PeopleToInclude = peopleFilters,
                PeopleToExclude = peopleExclusions,
                OrganisationsToInclude = organisationFilters,
                OrganisationsToExclude = organisationExclusions,
                ProgrammesToInclude = programmeFilters,
                ProgrammesToExclude = programmeExclusions,
                ThemesToInclude = themeFilters,
                ThemesToExclude = themeExclusions,
                IncludeMetadata = true
            };
        }
        else
        {
            getTopNewsRequest = new GetTopNewsRequest(
                clientId: DefaultDehavillandConfigurations.DehavillandCompanyId,
                clientName: DefaultDehavillandConfigurations.DehavillandCompanyName,
                query: stream.AiGeneratedPrompt)
            {
                TypeCodesToInclude = typeCodeFilters.TypeCodesToInclude,
                TypeCodesToExclude = typeCodeFilters.TypeCodesToExclude,
                FewshotsIds = streamFewshotFilters.FewshotIds,
                FewshotsWeight = streamFewshotFilters.FewshotWeight,
                AmplifiersTerms = streamAmplifiersFilters.AmplifiersTerms,
                AmplifiersWeight = streamAmplifiersFilters.AmplifiersWeight,
                PeopleToInclude = peopleFilters,
                PeopleToExclude = peopleExclusions,
                OrganisationsToInclude = organisationFilters,
                OrganisationsToExclude = organisationExclusions,
                ProgrammesToInclude = programmeFilters,
                ProgrammesToExclude = programmeExclusions,
                ThemesToInclude = themeFilters,
                ThemesToExclude = themeExclusions,
                IncludeMetadata = true
            };
        }

        var colibriApiResult = await _colibriApi.GetTopNewsAsync(
            getTopNewsRequest,
            cancellationToken);

        if (colibriApiResult.IsFailed)
        {
            _logger.LogError(
                "Failed to access GetTopNews from the Colibri API service for Feed Stream ID: {StreamId}",
                stream.Id.Value);

            return Result.Fail(colibriApiResult.Errors);
        }

        var valueResults = colibriApiResult.Value.Result;

        double effectiveConfidence = stream.AiModelConfidence * 100;

        var result = valueResults
            .Where(r => r.Score >= effectiveConfidence)
            .Select(r => new FeedDataItem(
                Id: r.Id,
                Title: r.Heading,
                SourceLink: r.Url,
                TypecodeName: r.Typecode,
                Date: DateTimeOffset.FromUnixTimeSeconds((long)r.Metadata.Timestamp))
            {
                People = r.Metadata.People?.ToList() ?? new List<string>(),
                Theme = new List<string>(), // Theme is not available in Metadata
                Organisations = r.Metadata.Organisations?.ToList() ?? new List<string>(),
                Programmes = r.Metadata.Programmes?.ToList() ?? new List<string>()
            });

        return Result.Ok(result);
    }

    private static (List<string> TypeCodesToInclude, List<string> TypeCodesToExclude) GetTypeCodeFilters(
        IReadOnlyCollection<StreamFilterTypeCode> streamFilterTypeCode)
    {
        var included = streamFilterTypeCode
            .Where(x => x.IncludeFlag)
            .Select(t => t.TypeCode.Name)
            .ToList();

        var excluded = streamFilterTypeCode
            .Where(x => !x.IncludeFlag)
            .Select(t => t.TypeCode.Name)
            .ToList();

        return (included, excluded);
    }

    private static (List<string> AmplifiersTerms, double AmplifiersWeight) GetAmplifiersFilters(
        IReadOnlyCollection<Amplifier> amplifiers)
    {
        var amplifierTerms = amplifiers.Select(a => a.Name).ToList();
        var amplifierWeight = amplifiers.FirstOrDefault()?.Weight ?? DefaultWeightValue;

        return (amplifierTerms, amplifierWeight);
    }

    private static (List<long> FewshotIds, double FewshotWeight) GetFewshotFilters(
        IReadOnlyCollection<FewShot> fewShots)
    {
        var fewshotIds = fewShots.Select(f => f.ItemId).ToList();
        var fewshotWeight = fewShots.FirstOrDefault()?.Weight ?? DefaultWeightValue;

        return (fewshotIds, fewshotWeight);
    }
}
