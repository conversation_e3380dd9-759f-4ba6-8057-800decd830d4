using SharedKernel.Entities;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.Streams.GetStreamItemRationale;

public class GetStreamItemRationaleEndpoint : EndpointBaseAsync
    .WithRequest<GetStreamItemRationaleRequest>
    .WithResult<ActionResult<GetStreamItemRationaleResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetStreamItemRationaleEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpGet($"{ApiRoute.StreamRoute}/{{id}}/items/{{streamItemId}}/rationale")]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(GetStreamItemRationaleResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<GetStreamItemRationaleResponse>> HandleAsync([FromRoute] GetStreamItemRationaleRequest request, CancellationToken cancellationToken = new())
    {
        var commandResult = await _sender.Send(
            new GetStreamItemRationaleQuery(new StreamId(request.Id), request.StreamItemId), cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Ok(_mapper.Map<GetStreamItemRationaleResponse>(commandResult.Value));
    }
}
