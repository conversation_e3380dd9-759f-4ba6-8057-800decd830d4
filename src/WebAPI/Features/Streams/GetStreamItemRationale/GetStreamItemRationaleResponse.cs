using WebAPI.Infrastructure.Mappers;

namespace WebAPI.Features.Streams.GetStreamItemRationale;

public sealed record GetStreamItemRationaleResponse : IMapFrom<ArticleRationale>
{
    public Guid StreamId { get; init; }
    public int StreamItemId { get; init; }
    public string Contents { get; init; }

    public void Mapping(Profile profile)
    {
        profile.CreateMap<ArticleRationale, GetStreamItemRationaleResponse>()
            .ForMember(destination => destination.StreamId,
                source => source.MapFrom(s => s.StreamId.Value));
    }
}
