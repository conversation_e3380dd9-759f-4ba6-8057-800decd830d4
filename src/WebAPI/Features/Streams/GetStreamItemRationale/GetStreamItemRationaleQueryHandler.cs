using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.GetArticleRationale;
using WebAPI.Infrastructure.Static;
using WebAPI.Persistence;
using Result = FluentResults.Result;

namespace WebAPI.Features.Streams.GetStreamItemRationale;

public sealed record ArticleRationale(StreamId StreamId, int StreamItemId, string Contents);

internal sealed class GetStreamItemRationaleQueryHandler : IQueryHandler<GetStreamItemRationaleQuery, ArticleRationale>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IColibriApi _colibriApi;
    private readonly IOpenSearchService _openSearchService;
    private readonly ILogger<GetStreamItemRationaleQueryHandler> _logger;

    public GetStreamItemRationaleQueryHandler(
        ApplicationDbContext dbContext,
        I<PERSON>olibri<PERSON><PERSON> colibriApi,
        ILogger<GetStreamItemRationaleQueryHandler> logger,
        IOpenSearchService openSearchService)
    {
        _dbContext = dbContext;
        _colibriApi = colibriApi;
        _logger = logger;
        _openSearchService = openSearchService;
    }

    public async Task<Result<ArticleRationale>> Handle(GetStreamItemRationaleQuery request,
        CancellationToken cancellationToken)
    {
        var stream = await _dbContext.Streams
            .Include(s => s.AiModel)
            .AsNoTracking()
            .SingleOrDefaultAsync(s => s.Id == request.StreamId, cancellationToken);

        if (stream is null)
        {
            return Result.Fail(new Error($"Stream with {request.StreamId.Value} not found", new Error("StreamId")));
        }

        var streamItem = await _openSearchService.GetById(request.ItemId, cancellationToken);

        if (streamItem is null)
        {
            return Result.Fail(new Error($"The stream item with id {request.ItemId} was not found", new Error("StreamItemId")));
        }

        var streamAiModel = stream.AiModel;

        var colibriApiResult = await _colibriApi.GetArticleRationaleAsync(new GetArticleRationaleRequest(
            clientId: DefaultDehavillandConfigurations.DehavillandCompanyId,
            clientName: DefaultDehavillandConfigurations.DehavillandCompanyName,
            clientProfile: stream.AiGeneratedPrompt,
            modelName: streamAiModel.Name.ToLowerInvariant(),
            article: streamItem.Content
            ), cancellationToken);

        if (colibriApiResult.IsFailed)
        {
            _logger.LogError(
                "Failed to access GetArticleRationaleAsync from the Colibri API service for Stream ID: {StreamId} and Stream Item ID: {StreamItemId}",
                request.StreamId.Value, request.ItemId);

            return Result.Fail(colibriApiResult.Errors);
        }

        return new ArticleRationale(request.StreamId, request.ItemId, colibriApiResult.Value.Response);
    }
}
