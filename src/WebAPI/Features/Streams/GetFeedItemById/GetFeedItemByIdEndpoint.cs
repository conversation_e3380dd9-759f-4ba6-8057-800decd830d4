using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.Streams.GetFeedItemById;

[Route(ApiRoute.StreamRoute)]
public class GetFeedItemByIdEndpoint : EndpointBaseAsync
    .WithRequest<int>
    .WithResult<ActionResult<GetFeedItemByIdResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetFeedItemByIdEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpGet("feed-item/{itemId}")]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(GetFeedItemByIdResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<GetFeedItemByIdResponse>> HandleAsync([FromRoute] int itemId, CancellationToken cancellationToken = default)
    {
        var query = new GetFeedItemByIdQuery(itemId);

        var queryResult = await _sender.Send(query, cancellationToken);

        if (queryResult.IsFailed)
        {
            return Problem(statusCode: StatusCodes.Status400BadRequest, detail: queryResult.Errors[0].Message);
        }

        return Ok(_mapper.Map<GetFeedItemByIdResponse>(queryResult.Value));
    }
}
