using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services;
using WebAPI.Persistence;
using Result = FluentResults.Result;

namespace WebAPI.Features.Streams.GetFeedItemById;

internal sealed class GetFeedItemByIdQueryHandler : IQueryHandler<GetFeedItemByIdQuery, ElasticSearchContent>
{
    private readonly IOpenSearchService _openSearchService;

    public GetFeedItemByIdQueryHandler(
        IOpenSearchService openSearchService,
        ApplicationDbContext dbContext,
        ICurrentUserService currentUserService)
    {
        _openSearchService = openSearchService;
    }

    public async Task<Result<ElasticSearchContent>> Handle(GetFeedItemByIdQuery request,
        CancellationToken cancellationToken)
    {
        // Get the feed item from OpenSearch
        var feedItem = await _openSearchService.GetById(request.ItemId, cancellationToken);

        if (feedItem is null)
        {
            return Result.Fail(new Error($"The feed item with id {request.ItemId} was not found", new Error("FeedItemId")));
        }

        return feedItem;
    }
}
