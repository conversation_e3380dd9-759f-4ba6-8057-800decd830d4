using System.Text.Json.Serialization;
using SharedKernel.Services;
using WebAPI.Infrastructure.Mappers;

namespace WebAPI.Features.Streams.GetFeedItemById;

public sealed record GetFeedItemByIdResponse : IMapFrom<ElasticSearchContent>
{
    [JsonPropertyName("id")]
    public int Id { get; init; }
    public string Title { get; init; }
    public DateTimeOffset Date { get; init; }
    public string TypecodeName { get; init; }
    public string SourceLink { get; init; }
    public string Analysis { get; init; }
    public string Content { get; init; }
    public int NofMentions { get; init; }

    // Implementation of Mapping
    public void Mapping(Profile profile)
    {
        profile.CreateMap<ElasticSearchContent, GetFeedItemByIdResponse>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.PoliticalId))
            .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Heading))
            .ForMember(dest => dest.TypecodeName, opt => opt.MapFrom(src => src.TypeCodeName))
            .ForMember(dest => dest.Content, opt => opt.MapFrom(src => src.Content))
            .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateTimeOffset.UtcNow)) // Default date - actual date field not available in current model
            .ForMember(dest => dest.SourceLink, opt => opt.MapFrom(src => src.Url)) // Default empty - source link field not available in current model
            .ForMember(dest => dest.Analysis, opt => opt.MapFrom(src => src.Analysis)) // Default empty - analysis field not available in current model
            .ForMember(dest => dest.NofMentions, opt => opt.MapFrom(src => 0)); // Default 0 - mentions count not available in current model
    }
}
