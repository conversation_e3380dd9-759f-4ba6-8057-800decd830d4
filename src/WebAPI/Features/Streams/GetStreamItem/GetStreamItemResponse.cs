using System.Text.Json.Serialization;
using SharedKernel.Services;
using WebAPI.Infrastructure.Mappers;

namespace WebAPI.Features.Streams.GetStreamItem;

public sealed record GetStreamItemResponse : IMapFrom<ElasticSearchContent>
{
    [JsonPropertyName("id")]
    public int PoliticalId { get; init; }
    public string Contents { get; init; }

    // Implementation of Mapping
    public void Mapping(Profile profile)
    {
        profile.CreateMap<ElasticSearchContent, GetStreamItemResponse>()
            .ForMember(dest => dest.Contents, opt => opt.MapFrom(src => src.Content));
    }

}
