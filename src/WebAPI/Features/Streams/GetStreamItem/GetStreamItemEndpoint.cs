using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.Streams.GetStreamItem;

public class GetStreamItemsEndpoint : EndpointBaseAsync
    .WithRequest<int>
    .WithResult<ActionResult<GetStreamItemResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetStreamItemsEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpGet($"{ApiRoute.StreamRoute}/items/{{id}}")]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IEnumerable<GetStreamItemResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<GetStreamItemResponse>> HandleAsync([FromRoute] int id, CancellationToken cancellationToken = new())
    {
        var commandResult = await _sender.Send(
            new GetStreamItemQuery(id), cancellationToken);

        if (commandResult.IsFailed)
        {
            return Problem(statusCode: StatusCodes.Status404NotFound, detail: commandResult.Errors[0].Message);
        }

        return Ok(_mapper.Map<GetStreamItemResponse>(commandResult.Value));
    }
}
