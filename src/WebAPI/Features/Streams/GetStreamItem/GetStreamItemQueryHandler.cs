using OpenSearch.Client;
using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services;
using WebAPI.Persistence;
using Result = FluentResults.Result;

namespace WebAPI.Features.Streams.GetStreamItem;

internal sealed class GetStreamItemQueryHandler : IQueryHandler<GetStreamItemQuery, ElasticSearchContent>
{
    private readonly IOpenSearchService _openSearchService;
    private readonly ApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;

    public GetStreamItemQueryHandler(
        IOpenSearchClient openSearchClient,
        IOpenSearchService openSearchService,
        ApplicationDbContext dbContext,
        ICurrentUserService currentUserService
        )
    {
        _openSearchService = openSearchService;
        _dbContext = dbContext;
        _currentUserService = currentUserService;
    }

    public async Task<Result<ElasticSearchContent>> Handle(GetStreamItemQuery request,
        CancellationToken cancellationToken)
    {
        var userId = Guid.Parse(_currentUserService.Id);
        var streamItem = await _openSearchService.GetById(request.ItemId, cancellationToken);

        if (streamItem is null)
        {
            return Result.Fail(new Error($"The stream item with id {request.ItemId} was not found", new Error("StreamItemId")));
        }

        var streamItemRead = StreamItemReadByUser.Create(userId, request.ItemId);

        var existingStreamItemRead = await _dbContext.StreamItemReadByUser.SingleOrDefaultAsync(
            s => s.UserId == userId &&
            s.StreamItemId == request.ItemId,
            cancellationToken);

        if (existingStreamItemRead is null)
        {
            await _dbContext.StreamItemReadByUser.AddAsync(streamItemRead, cancellationToken);
            await _dbContext.SaveChangesAsync(cancellationToken);
        }

        return streamItem;
    }
}
