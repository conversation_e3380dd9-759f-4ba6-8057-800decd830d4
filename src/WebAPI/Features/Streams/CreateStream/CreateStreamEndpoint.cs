using System.Net;
using WebAPI.Features.Streams.Shared;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.Streams.CreateStream;

public class CreateStreamEndpoint : EndpointBaseAsync
    .WithRequest<CreateRequestResponse>
    .WithoutResult
{
    private readonly ISender _sender;

    public CreateStreamEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpPost(ApiRoute.StreamRoute)]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<Response>> HandleAsync([FromBody] CreateRequestResponse request, CancellationToken cancellationToken = default)
    {
        var commandResult = await _sender.Send(
            new CreateStreamCommand(request), cancellationToken);

        if (commandResult.IsFailed)
        {
            var httpStatus = commandResult.Errors.FirstOrDefault()?.Metadata?.FirstOrDefault().Value;

            if (httpStatus != null && Enum.TryParse(typeof(HttpStatusCode), httpStatus.ToString(), out var statusCode))
            {
                return (HttpStatusCode)statusCode switch
                {
                    HttpStatusCode.BadRequest => Problem(
                        statusCode: StatusCodes.Status400BadRequest,
                        detail: commandResult.Errors.FirstOrDefault()?.Message),

                    HttpStatusCode.Unauthorized => Problem(
                        statusCode: StatusCodes.Status401Unauthorized,
                        detail: commandResult.Errors.FirstOrDefault()?.Message),

                    HttpStatusCode.Forbidden => Problem(
                        statusCode: StatusCodes.Status403Forbidden,
                        detail: commandResult.Errors.FirstOrDefault()?.Message),

                    HttpStatusCode.NotFound => Problem(
                        statusCode: StatusCodes.Status404NotFound,
                        detail: commandResult.Errors.FirstOrDefault()?.Message),

                    _ => Problem(commandResult.Errors.FirstOrDefault()?.Message)
                };
            }
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Created($"{ApiRoute.StreamRoute}/{commandResult.Value.Id}", commandResult.Value);
    }
}
