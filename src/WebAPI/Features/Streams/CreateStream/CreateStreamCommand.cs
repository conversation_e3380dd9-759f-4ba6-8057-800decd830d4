using SharedKernel.Primitives.Interfaces;
using WebAPI.Features.Streams.Shared;

namespace WebAPI.Features.Streams.CreateStream;

public sealed record CreateStreamCommand(CreateRequestResponse Request) : ICommand<Response>;

internal sealed class CreateStreamCommandValidator : AbstractValidator<CreateStreamCommand>
{
    public CreateStreamCommandValidator()
    {
        RuleFor(x => x.Request.Name).NotEmpty();

        RuleFor(x => x.Request.Filters.TypeCodeIds).NotNull()
            .When(x => x.Request.Filters != null)
            .WithMessage("'Filters Type Code Ids' must not be null.");

        RuleFor(x => x.Request.ExclusionFilters).NotEmpty();
        RuleFor(x => x.Request.ExclusionFilters.People).NotNull()
            .When(x => x.Request.ExclusionFilters != null)
            .WithMessage("'Exclusion Filters People' must not be null.");
        RuleFor(x => x.Request.ExclusionFilters.Organisations).NotNull()
            .When(x => x.Request.ExclusionFilters != null)
            .WithMessage("'Exclusion Filters Organisation' must not be null.");
        RuleFor(x => x.Request.ExclusionFilters.Programmes).NotNull()
            .When(x => x.Request.ExclusionFilters != null)
            .WithMessage("'Exclusion Filters Programme' must not be null.");
        RuleFor(x => x.Request.ExclusionFilters.Themes).NotNull()
            .When(x => x.Request.ExclusionFilters != null)
            .WithMessage("'Exclusion Filters Themes' must not be null.");
        RuleFor(x => x.Request.ExclusionFilters.TypeCodeIds).NotNull()
            .When(x => x.Request.ExclusionFilters != null)
            .WithMessage("'Exclusion Filters Type Code' must not be null.");

        RuleFor(x => x.Request.IsMention).NotNull();

        // When a Mention Stream
        When(x => x.Request.IsMention, () =>
            {
                RuleFor(x => x.Request.Prompt).Empty()
                    .WithMessage("Mention Stream should not have a Prompt.");

                RuleFor(x => x.Request.AiGeneratedPrompt).Empty()
                    .WithMessage("Mention Stream should not have an AI Generated Prompt.");

                RuleFor(x => x.Request.AdvancedSettings).Empty()
                    .WithMessage("Mention Stream should not have Advanced Settings.");

                RuleFor(x => x.Request.AiParameters).Empty()
                    .WithMessage("Mention Stream should not have AI Parameters.");

                RuleFor(x => x.Request.Filters).NotEmpty();
                RuleFor(x => x.Request.Filters)
                    .Must(HaveAtLeastOneFilterPopulated)
                    .When(x => x.Request.Filters != null)
                    .WithMessage("At least one of People, Organisation, Programme or Theme must contain a filter.");
            })
            // When not a Mention Stream
            .Otherwise(() =>
            {
                RuleFor(x => x.Request.Prompt).NotEmpty();

                RuleFor(x => x.Request.AiGeneratedPrompt).NotEmpty();

                RuleFor(x => x.Request.AdvancedSettings).NotEmpty();

                RuleFor(x => x.Request.AdvancedSettings.FewShotItemIds).NotNull()
                    .When(x => x.Request.AdvancedSettings != null)
                    .WithMessage("'Few Shot Item Ids' must not be null.");

                RuleFor(x => x.Request.AdvancedSettings.FewShotRelevancy).NotNull()
                    .When(x => x.Request.AdvancedSettings != null)
                    .WithMessage("'Few Shot Relevancy' must not be null.");

                RuleFor(x => x.Request.AdvancedSettings.Amplifiers).NotNull()
                    .When(x => x.Request.AdvancedSettings != null)
                    .WithMessage("'Advanced Settings Amplifiers' must not be null.");

                RuleFor(x => x.Request.AdvancedSettings.AmplifierRelevancy).NotNull()
                    .When(x => x.Request.AdvancedSettings != null)
                    .WithMessage("'Advanced Settings Amplifier Relevancy' must not be null.");

                RuleFor(x => x.Request.AdvancedSettings)
                    .Must(settings =>
                        settings.FewShotItemIds != null &&
                        settings.FewShotItemIds.Any() == settings.FewShotRelevancy > 0)
                    .When(x => x.Request.AdvancedSettings != null)
                    .WithMessage(
                        "If Few Shot Item Id's has value, Few Shot Relevancy must be greater than zero."); // And vice versa

                RuleFor(x => x.Request.AdvancedSettings)
                    .Must(settings =>
                        settings.Amplifiers != null && settings.Amplifiers.Any() == settings.AmplifierRelevancy > 0)
                    .When(x => x.Request.AdvancedSettings != null)
                    .WithMessage(
                        "If Amplifiers has value, Amplifier Relevancy must be greater than zero."); // And vice versa

                RuleFor(x => x.Request.AiParameters).NotEmpty();

                RuleFor(x => x.Request.AiParameters.AiModelIds).NotEmpty()
                    .When(x => x.Request.AiParameters != null);

                RuleFor(x => x.Request.AiParameters.Confidence).NotEmpty() // Will allow 0
                    .When(x => x.Request.AiParameters != null);
            });
    }

    private static bool HaveAtLeastOneFilterPopulated(Filters filters)
    {
        return filters.Organisations.Any() || filters.People.Any() || filters.Programmes.Any() || filters.Themes.Any();
    }
}
