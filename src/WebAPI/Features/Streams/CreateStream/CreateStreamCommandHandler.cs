using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.AddAmplifiers;
using SharedKernel.Services.ColibriApi.AddFewShots;
using SharedKernel.Services.ColibriApi.ExpandClientProfile;
using SharedKernel.Services.UserApi;
using WebAPI.Features.Streams.Shared;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Infrastructure.Static;
using WebAPI.Persistence;

namespace WebAPI.Features.Streams.CreateStream;

internal sealed class CreateStreamCommandHandler : ICommandHandler<CreateStreamCommand, Response>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IUserApiClient _userClient;
    private readonly IColibriApi _colibriApi;
    private readonly ILogger<CreateStreamCommandHandler> _logger;

    public CreateStreamCommandHandler(
        ApplicationDbContext dbContext,
        IUserApiClient userClient,
        IColibriApi colibriApi,
        ILogger<CreateStreamCommandHandler> logger)
    {
        _dbContext = dbContext;
        _userClient = userClient;
        _colibriApi = colibriApi;
        _logger = logger;
    }

    public async Task<Result<Response>> Handle(CreateStreamCommand createStreamCommand,
        CancellationToken cancellationToken)
    {
        string companyName = string.Empty;

        if (createStreamCommand.Request.CompanyId != Guid.Empty && createStreamCommand.Request.CompanyId != null)
        {
            var companyNameResult = await GetCompanyName(createStreamCommand.Request.CompanyId, cancellationToken);

            if (companyNameResult.IsFailed)
            {
                return Result.Fail(companyNameResult.Errors);
            }

            companyName = companyNameResult.Value ?? string.Empty;
        }
        var aiGeneratedPromptId = createStreamCommand.Request.AiGeneratedPromptId;
        if (aiGeneratedPromptId == -1)
        {
            var promptResult = await GenerateAiPrompt(createStreamCommand.Request.Prompt, cancellationToken);
            aiGeneratedPromptId = promptResult.Value.PromptId;
        }

        var stream = CreateStream(createStreamCommand, companyName, aiGeneratedPromptId);
        var amplifiers = CreateAmplifiers(createStreamCommand, stream.Id);
        var fewShots = CreateFewShots(createStreamCommand, stream.Id);
        var streamMetadataFilters = CreateStreamMetadataFilters(createStreamCommand, stream.Id);
        var streamFilterTypeCodes = await CreateStreamFilterTypeCodes(createStreamCommand, stream.Id, cancellationToken);

        await _dbContext.Streams.AddAsync(stream, cancellationToken);
        await AddEntitiesToContextAsync(_dbContext.StreamMetadataFilter, streamMetadataFilters, cancellationToken);
        await AddEntitiesToContextAsync(_dbContext.StreamFilterTypeCode, streamFilterTypeCodes, cancellationToken);

        if (amplifiers.Count > 0)
        {
            var handleAddAmplifiers = await HandleAddAmplifiers(amplifiers, cancellationToken);
            if (handleAddAmplifiers.IsFailed)
            {
                return Result.Fail(handleAddAmplifiers.Errors);
            }

            await _dbContext.Amplifier.AddRangeAsync(amplifiers, cancellationToken);
        }

        if (fewShots.Count > 0)
        {
            var handleAddFewShots = await HandleAddFewShots(fewShots, cancellationToken);
            if (handleAddFewShots.IsFailed)
            {
                return Result.Fail(handleAddFewShots.Errors);
            }

            await _dbContext.FewShot.AddRangeAsync(fewShots, cancellationToken);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        return new Response(
            stream.Id.Value,
            createStreamCommand.Request.Name,
            createStreamCommand.Request.CompanyId,
            createStreamCommand.Request.Prompt,
            createStreamCommand.Request.AiGeneratedPrompt,
            createStreamCommand.Request.IsMention,
            createStreamCommand.Request.Filters,
            createStreamCommand.Request.ExclusionFilters,
            createStreamCommand.Request.AiParameters,
            createStreamCommand.Request.AdvancedSettings
        );
    }

    #region Private Helper Methods

    private async Task<Result<string>> GetCompanyName(Guid? companyId, CancellationToken cancellationToken)
    {
        if (!companyId.HasValue)
        {
            return Result.Ok<string>(null);
        }

        var userResponse = await _userClient.GetCompanyName(companyId.Value, cancellationToken);

        return userResponse.IsFailed ? Result.Fail(userResponse.Errors) : userResponse.Value.Name;
    }

    private static Stream CreateStream(CreateStreamCommand createStreamCommand, string companyName, int aiGeneratedPromptId)
    {
        if (createStreamCommand.Request.IsMention)
        {
            return Stream.CreateMention(createStreamCommand.Request.Name, createStreamCommand.Request.CompanyId,
                companyName, null);
        }

        return Stream.Create(
            createStreamCommand.Request.CompanyId,
            new AiModelId(createStreamCommand.Request.AiParameters.AiModelIds.FirstOrDefault()),
            createStreamCommand.Request.Name,
            createStreamCommand.Request.Prompt,
            companyName,
            null,
            createStreamCommand.Request.AiGeneratedPrompt,
            aiGeneratedPromptId,
            createStreamCommand.Request.AiParameters.Confidence
        );
    }

    private static List<Amplifier> CreateAmplifiers(CreateStreamCommand createStreamCommand, StreamId streamId)
    {
        if (createStreamCommand.Request.AdvancedSettings is null)
        {
            return [];
        }

        return createStreamCommand.Request.AdvancedSettings.Amplifiers
            .Select(name => Amplifier.Create(
                streamId,
                name.ToLowerInvariant(),
                createStreamCommand.Request.AdvancedSettings.AmplifierRelevancy.Value))
            .ToList();
    }

    private static List<FewShot> CreateFewShots(CreateStreamCommand createStreamCommand, StreamId streamId)
    {
        if (createStreamCommand.Request.AdvancedSettings is null)
        {
            return [];
        }

        return createStreamCommand.Request.AdvancedSettings.FewShotItemIds
            .Select(itemId => FewShot.Create(
                streamId,
                itemId.Value,
                createStreamCommand.Request.AdvancedSettings.FewShotRelevancy.Value))
            .ToList();
    }

    private static List<StreamMetadataFilter> CreateStreamMetadataFilters(CreateStreamCommand createStreamCommand,
        StreamId streamId)
    {
        var streamFilters = new List<StreamMetadataFilter>();

        AddFiltersToList(streamFilters, createStreamCommand.Request.Filters.People, streamId,
            StreamMetadataFilterName.People, true);
        AddFiltersToList(streamFilters, createStreamCommand.Request.Filters.Organisations, streamId,
            StreamMetadataFilterName.Organisation, true);
        AddFiltersToList(streamFilters, createStreamCommand.Request.Filters.Programmes, streamId,
            StreamMetadataFilterName.Programme, true);
        AddFiltersToList(streamFilters, createStreamCommand.Request.Filters.Themes, streamId,
            StreamMetadataFilterName.Theme, true);
        AddFiltersToList(streamFilters, createStreamCommand.Request.ExclusionFilters.People, streamId,
            StreamMetadataFilterName.People, false);
        AddFiltersToList(streamFilters, createStreamCommand.Request.ExclusionFilters.Organisations, streamId,
            StreamMetadataFilterName.Organisation, false);
        AddFiltersToList(streamFilters, createStreamCommand.Request.ExclusionFilters.Programmes, streamId,
            StreamMetadataFilterName.Programme, false);
        AddFiltersToList(streamFilters, createStreamCommand.Request.ExclusionFilters.Themes, streamId,
            StreamMetadataFilterName.Theme, false);

        return streamFilters;
    }

    private static void AddFiltersToList(List<StreamMetadataFilter> streamFilters, IEnumerable<Filter> filterValues,
        StreamId streamId, StreamMetadataFilterName filterName, bool include)
    {
        if (filterValues is null)
        {
            return;
        }

        foreach (var filter in filterValues)
        {
            streamFilters.Add(StreamMetadataFilter.Create(streamId, filterName, include, filter.Name));
        }
    }

    private async Task<List<StreamFilterTypeCode>> CreateStreamFilterTypeCodes(CreateStreamCommand createStreamCommand,
        StreamId streamId, CancellationToken cancellationToken)
    {
        var streamFilterTypeCodes = new List<StreamFilterTypeCode>();

        var inflatedTypeCodeIdFilters =
            await _dbContext.GetInflatedTypeCodesAsync(createStreamCommand.Request.Filters.TypeCodeIds,
                cancellationToken);

        streamFilterTypeCodes.AddRange(
            inflatedTypeCodeIdFilters
                .Select(filter => StreamFilterTypeCode.Create(filter, streamId, true))
        );

        var inflatedTypeCodeIdExclusionFilters =
            await _dbContext.GetInflatedTypeCodesAsync(createStreamCommand.Request.ExclusionFilters.TypeCodeIds,
                cancellationToken);

        streamFilterTypeCodes.AddRange(
            inflatedTypeCodeIdExclusionFilters.Select(filter =>
                StreamFilterTypeCode.Create(filter, streamId, false))
        );

        return streamFilterTypeCodes;
    }

    private static async Task AddEntitiesToContextAsync<T>(DbSet<T> dbSet, List<T> entities,
        CancellationToken cancellationToken) where T : class
    {
        if (entities.Count > 0)
        {
            await dbSet.AddRangeAsync(entities, cancellationToken);
        }
    }


    private async Task<Result> HandleAddAmplifiers(IReadOnlyList<Amplifier> amplifiers,
        CancellationToken cancellationToken)
    {
        var amplifiersTerms = amplifiers.Select(x => x.Name).ToList();
        return await HandleColibriApiRequest(
            () => _colibriApi.AddAmplifiers(new AddAmplifiersRequest(amplifiersTerms), cancellationToken),
            nameof(HandleAddAmplifiers),
            "amplifiers",
            response => response.ErrorAmplifiers
        );
    }

    private async Task<Result> HandleAddFewShots(IReadOnlyList<FewShot> fewShots, CancellationToken cancellationToken)
    {
        var fewShotsIds = fewShots.Select(x => x.ItemId).ToList();
        return await HandleColibriApiRequest(
            () => _colibriApi.AddFewShots(new AddFewShotsRequest(fewShotsIds), cancellationToken),
            nameof(HandleAddFewShots),
            "fewshots",
            response => response.ErrorFewShots
        );
    }

    private async Task<Result> HandleColibriApiRequest<TResponse, TError>(
        Func<Task<Result<TResponse>>> apiCall,
        string methodName,
        string entityName,
        Func<TResponse, IReadOnlyList<TError>> errorExtractor)
    {
        var colibriApiResult = await apiCall();

        if (colibriApiResult.IsFailed)
        {
            _logger.LogError("Failed to access {MethodName} from the Colibri API service", methodName);
            return Result.Fail(colibriApiResult.Errors);
        }

        var colibriResponseValue = colibriApiResult.Value;
        var errors = errorExtractor(colibriResponseValue);

        if (errors?.Count > 0)
        {
            using (_logger.BeginScope("{@Errors}", errors))
            {
                _logger.LogError("Failed to generate {EntityName} with Colibri API service", entityName);
            }

            return Result.Fail(new Error($"Failed to generate {entityName} from Colibri API service",
                new Error($"{entityName}Term")));
        }

        return Result.Ok();
    }

    private async Task<Result<ExpandClientProfileResponse>> GenerateAiPrompt(string prompt, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Using {ModelName} model", DefaultDehavillandConfigurations.ModelName);

        var colibriApiResult = await _colibriApi.ExpandClientProfileAsync(
            new ExpandClientProfileRequest(
                clientId: DefaultDehavillandConfigurations.DehavillandCompanyId,
                clientName: DefaultDehavillandConfigurations.DehavillandCompanyName,
                clientProfile: prompt,
                modelName: DefaultDehavillandConfigurations.ModelName),
            cancellationToken);

        if (colibriApiResult.IsFailed)
        {
            _logger.LogError(
                "Failed to access ExpandClientProfile from the Colibri API service for Company ID: {CompanyId}, Company Name: {CompanyName}",
                DefaultDehavillandConfigurations.DehavillandCompanyId,
                DefaultDehavillandConfigurations.DehavillandCompanyName);

            return Result.Fail(colibriApiResult.Errors);
        }

        return colibriApiResult.Value;
    }
    #endregion
}
