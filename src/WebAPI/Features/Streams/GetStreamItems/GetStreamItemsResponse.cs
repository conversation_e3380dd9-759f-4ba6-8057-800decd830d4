using WebAPI.Infrastructure.Mappers;

namespace WebAPI.Features.Streams.GetStreamItems;

public sealed record GetStreamItemsResponse : IMapFrom<StreamItem>
{
    public int Id { get; init; }
    public string Title { get; init; }
    public double Relevance { get; init; }
    public DateTimeOffset Date { get; init; }
    public string TypecodeName { get; init; }
    public string SourceLink { get; init; }
    public bool IsRead { get; init; }
}
