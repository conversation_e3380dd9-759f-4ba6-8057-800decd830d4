namespace WebAPI.Features.Streams.GetStreamItems;

public class GetStreamItemsRequest
{
    [FromRoute(Name = "id")]
    public Guid Id { get; set; } // Bind StreamId from the route

    [FromQuery]
    public double? Confidence { get; set; } // Bind Confidence from query

    [FromQuery]
    public DateTime? StartDate { get; set; } // Bind StartDate from query

    [FromQuery]
    public DateTime? EndDate { get; set; } // Bind EndDate from query
}
