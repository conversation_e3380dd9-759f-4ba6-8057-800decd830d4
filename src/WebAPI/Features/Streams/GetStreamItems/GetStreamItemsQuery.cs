using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;

namespace WebAPI.Features.Streams.GetStreamItems;

public sealed record GetStreamItemsQuery(StreamId StreamId, double? Confidence = null, DateTime? StartDate = null, DateTime? EndDate = null)
    : IQuery<IEnumerable<StreamItem>>;


internal sealed class GetStreamItemsQueryValidator : AbstractValidator<GetStreamItemsQuery>
{
    public GetStreamItemsQueryValidator()
    {
        RuleFor(x => x.StreamId.Value).NotEmpty();

        // If both dates have values, start date must be before end date.
        When(x => x.StartDate.HasValue && x.EndDate.HasValue, () =>
        {
            RuleFor(x => x).Must(x => x.StartDate <= x.EndDate)
                .WithMessage("StartDate must be less than or equal to EndDate");
        });

        // Confidence to be between 0 and 1 if it's provided
        RuleFor(x => x.Confidence)
            .InclusiveBetween(0, 1)
            .When(x => x.Confidence.HasValue)
            .WithMessage("Confidence must be between 0 and 1");
    }
}
