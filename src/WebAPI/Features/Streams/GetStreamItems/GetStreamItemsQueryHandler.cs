using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.GetTopNews;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Infrastructure.Static;
using WebAPI.Persistence;

namespace WebAPI.Features.Streams.GetStreamItems;

public record StreamItem(int Id, string Title, string SourceLink, double Relevance, string TypecodeName, DateTimeOffset Date)
{
    public bool IsRead { get; set; }
};

internal sealed record FilterSelector<TId>(TId Id, string Value);

internal sealed class GetStreamItemsQueryHandler : IQueryHandler<GetStreamItemsQuery, IEnumerable<StreamItem>>
{
    private const double DetaultWeightValue = 0.25D;

    private readonly IColibriApi _colibriApi;
    private readonly ILogger<GetStreamItemsQueryHandler> _logger;
    private readonly ApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;

    public GetStreamItemsQueryHandler(
        IColibriApi colibriApi,
        ILogger<GetStreamItemsQueryHandler> logger,
        ApplicationDbContext dbContext,
        ICurrentUserService currentUserService)
    {
        _colibriApi = colibriApi;
        _logger = logger;
        _dbContext = dbContext;
        _currentUserService = currentUserService;
    }

    public async Task<Result<IEnumerable<StreamItem>>> Handle(GetStreamItemsQuery request, CancellationToken cancellationToken)
    {
        var stream = await _dbContext
            .Streams
            .Include(s => s.StreamFilterTypeCodes)
            .ThenInclude(sft => sft.TypeCode)
            .Include(s => s.Amplifiers)
            .Include(stream => stream.FewShots)
            .Include(s => s.StreamMetadataFilters)
            .AsNoTracking()
            .SingleOrDefaultAsync(s => s.Id == request.StreamId, cancellationToken);

        if (stream is null)
        {
            return Result.Fail(new Error($"Stream with {request.StreamId.Value} not found", new Error("StreamId")));
        }

        var typeCodeFilters = GetTypeCodeFilters(stream.StreamFilterTypeCodes);
        var streamAmplifiersFilters = GetAmplifiersFilters(stream.Amplifiers);
        var streamFewshotFilters = GetFewshotFilters(stream.FewShots);
        var peopleFilters = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.People, true)
            .Select(x => x.Name).ToList();
        var peopleExclusions = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.People, false)
            .Select(x => x.Name).ToList();
        var organisationFilters = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Organisation, true)
            .Select(x => x.Name).ToList();
        var organisationExclusions = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Organisation, false)
            .Select(x => x.Name).ToList();
        var programmeFilters = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Programme, true)
            .Select(x => x.Name).ToList();
        var programmeExclusions = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Programme, false)
            .Select(x => x.Name).ToList();
        var themeFilters = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Theme, true)
            .Select(x => x.Name).ToList();
        var themeExclusions = stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Theme, false)
            .Select(x => x.Name).ToList();

        GetTopNewsRequest getTopNewsRequest;
        if (stream.AiGeneratedPromptId is > 0)
        {
            getTopNewsRequest = new GetTopNewsRequest(
                clientId: DefaultDehavillandConfigurations.DehavillandCompanyId,
                clientName: DefaultDehavillandConfigurations.DehavillandCompanyName,
                queryId: stream.AiGeneratedPromptId.Value)
            {
                TypeCodesToInclude = typeCodeFilters.TypeCodesToInclude,
                TypeCodesToExclude = typeCodeFilters.TypeCodesToExclude,
                FewshotsIds = streamFewshotFilters.FewshotIds,
                FewshotsWeight = streamFewshotFilters.FewshotWeight,
                AmplifiersTerms = streamAmplifiersFilters.AmplifiersTerms,
                AmplifiersWeight = streamAmplifiersFilters.AmplifiersWeight,
                PeopleToInclude = peopleFilters,
                PeopleToExclude = peopleExclusions,
                OrganisationsToInclude = organisationFilters,
                OrganisationsToExclude = organisationExclusions,
                ProgrammesToInclude = programmeFilters,
                ProgrammesToExclude = programmeExclusions,
                ThemesToInclude = themeFilters,
                ThemesToExclude = themeExclusions,
                BegginingTimestamp = request.StartDate,
                EndTimestamp = request.EndDate,
                ScoreThreshold = stream.AiModelConfidence * 100,
                IncludeMetadata = true
            };
        }
        else
        {
            getTopNewsRequest = new GetTopNewsRequest(
                clientId: DefaultDehavillandConfigurations.DehavillandCompanyId,
                clientName: DefaultDehavillandConfigurations.DehavillandCompanyName,
                query: stream.AiGeneratedPrompt)
            {
                TypeCodesToInclude = typeCodeFilters.TypeCodesToInclude,
                TypeCodesToExclude = typeCodeFilters.TypeCodesToExclude,
                FewshotsIds = streamFewshotFilters.FewshotIds,
                FewshotsWeight = streamFewshotFilters.FewshotWeight,
                AmplifiersTerms = streamAmplifiersFilters.AmplifiersTerms,
                AmplifiersWeight = streamAmplifiersFilters.AmplifiersWeight,
                PeopleToInclude = peopleFilters,
                PeopleToExclude = peopleExclusions,
                OrganisationsToInclude = organisationFilters,
                OrganisationsToExclude = organisationExclusions,
                ProgrammesToInclude = programmeFilters,
                ProgrammesToExclude = programmeExclusions,
                ThemesToInclude = themeFilters,
                ThemesToExclude = themeExclusions,
                BegginingTimestamp = request.StartDate,
                EndTimestamp = request.EndDate,
                IncludeMetadata = true
            };
        }

        var colibriApiResult = await _colibriApi.GetTopNewsAsync(
            getTopNewsRequest,
            cancellationToken);

        if (colibriApiResult.IsFailed)
        {
            _logger.LogError(
                "Failed to access GetTopNews from the Colibri API service for Stream ID: {StreamId}",
                stream.Id.Value);

            return Result.Fail(colibriApiResult.Errors);
        }

        var valueResults = colibriApiResult.Value.Result;

        double effectiveConfidence = (request.Confidence ?? stream.AiModelConfidence) * 100;

        var result = valueResults
            .Where(r => r.Score >= effectiveConfidence)
            .Select(r => new StreamItem(
                Id: r.Id,
                Title: r.Heading,
                SourceLink: r.Url,
                Relevance: r.Score,
                TypecodeName: r.Typecode,
                Date: DateTimeOffset.FromUnixTimeSeconds((long)r.Metadata.Timestamp)));

        result = await MarkViewedItems(result);

        return Result.Ok(result);
    }

    private async Task<IEnumerable<StreamItem>> MarkViewedItems(IEnumerable<StreamItem> streamItems)
    {
        // Materialize streamItems to prevent multiple enumerations
        var list = streamItems.ToList();

        // Create HashSet for fast membership checks
        var itemIds = new HashSet<int>(list.Select(item => item.Id));

        var userId = _currentUserService.Id;

        // Query only items matching user identity and result item IDs
        var userViewedItems = await _dbContext.StreamItemReadByUser
            .Where(i => i.UserId.ToString() == userId && itemIds.Contains(i.StreamItemId))
            .ToListAsync();

        // Index `streamItems` by Id to enable O(1) lookups
        var streamItemDictionary = list.ToDictionary(item => item.Id);

        foreach (var viewedItem in userViewedItems)
        {
            if (streamItemDictionary.TryGetValue(viewedItem.StreamItemId, out var streamItem))
            {
                streamItem.IsRead = true;
            }
        }

        return list;
    }

    private static (List<string> TypeCodesToInclude, List<string> TypeCodesToExclude) GetTypeCodeFilters(
        IReadOnlyCollection<StreamFilterTypeCode> streamFilterTypeCode)
    {
        var included = streamFilterTypeCode
            .Where(x => x.IncludeFlag)
            .Select(t => t.TypeCode.Name)
            .ToList();

        var excluded = streamFilterTypeCode
            .Where(x => !x.IncludeFlag)
            .Select(t => t.TypeCode.Name)
            .ToList();

        return (included, excluded);
    }

    private static (double AmplifiersWeight, List<string> AmplifiersTerms) GetAmplifiersFilters(IReadOnlyCollection<Amplifier> streamAmplifiers)
    {
        var amplifiersTerms = streamAmplifiers.Select(a => a.Name).ToList();
        var amplifiersWeight = streamAmplifiers.FirstOrDefault()?.Weight ?? DetaultWeightValue;

        return (amplifiersWeight, amplifiersTerms);
    }

    private static (double FewshotWeight, List<long> FewshotIds) GetFewshotFilters(IReadOnlyCollection<FewShot> streamFewshots)
    {
        var fewshotsTerms = streamFewshots.Select(a => a.ItemId).ToList();
        var fewshotsWeight = streamFewshots.FirstOrDefault()?.Weight ?? DetaultWeightValue;

        return (fewshotsWeight, fewshotsTerms);
    }
}
