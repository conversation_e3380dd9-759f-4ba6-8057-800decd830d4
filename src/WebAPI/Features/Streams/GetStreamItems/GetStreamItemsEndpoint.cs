using SharedKernel.Entities;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.Streams.GetStreamItems;

public class GetStreamItemsEndpoint : EndpointBaseAsync
    .WithRequest<GetStreamItemsRequest>
    .WithResult<ActionResult<GetStreamItemsResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetStreamItemsEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpGet($"{ApiRoute.StreamRoute}/{{id}}/items")]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IEnumerable<GetStreamItemsResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<GetStreamItemsResponse>> HandleAsync(
        GetStreamItemsRequest request,
        CancellationToken cancellationToken = default)
    {
        // Map inputs to the query object
        var query = new GetStreamItemsQuery(
            new StreamId(request.Id),
            request.Confidence,
            request.StartDate,
            request.EndDate);

        var commandResult = await _sender.Send(query, cancellationToken);

        if (commandResult.IsFailed)
        {
            return Problem(statusCode: StatusCodes.Status404NotFound, detail: commandResult.Errors[0].Message);
        }

        return Ok(_mapper.Map<IEnumerable<GetStreamItemsResponse>>(commandResult.Value));
    }
}
