using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Features.Streams.Shared;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Persistence;

namespace WebAPI.Features.Streams.GetStream;

#region Request Models

public sealed record GetStreamQuery(StreamId StreamId) : IQuery<Response>;

public sealed record Response(
    Guid Id,
    string Name,
    DateTimeOffset LastUpdatedUtc,
    Company Company,
    string Prompt,
    string AiGeneratedPrompt,
    int? AiGeneratedPromptId,
    bool IsMention,
    Filters Filters,
    ExclusionFilters ExclusionFilters,
    AiParameters AiParameters,
    AdvancedSettings AdvancedSettings
);

public sealed record Company(Guid? Id, string Name);

public sealed record People(Guid? Id, string FirstName, string Surname);

public sealed record Organisation(Guid? Id, string Name);

public sealed record Programme(Guid? Id, string Name);

public sealed record TypeCode(int? Id, string Name);

public sealed record Filters(
    IEnumerable<Filter> People,
    IEnumerable<Filter> Organisations,
    IEnumerable<Filter> Programmes,
    IEnumerable<Filter> Themes,
    IEnumerable<TypeCode> TypeCodes);

public sealed record ExclusionFilters(
    IEnumerable<Filter> People,
    IEnumerable<Filter> Organisations,
    IEnumerable<Filter> Programmes,
    IEnumerable<Filter> Themes,
    IEnumerable<TypeCode> TypeCodes);

public sealed record AiParameters(IEnumerable<AiModel> AiModels, double? Confidence);

public sealed record AiModel(Guid? Id, string Name);

public sealed record FewShot(Guid? Id, long? ItemId);

public sealed record AdvancedSettings(
    IEnumerable<string> Amplifiers,
    double? AmplifierRelevancy,
    IEnumerable<FewShot> FewShots,
    double? FewShotRelevancy);

internal sealed class GetStreamQueryValidator : AbstractValidator<GetStreamQuery>
{
    public GetStreamQueryValidator()
    {
        RuleFor(x => x.StreamId).NotEmpty();
    }
}

#endregion

#region Handler

internal sealed class GetStreamQueryHandler : IQueryHandler<GetStreamQuery, Response>
{
    private readonly ApplicationDbContext _dbContext;

    public GetStreamQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<Response>> Handle(GetStreamQuery request, CancellationToken cancellationToken)
    {
        var stream = await _dbContext.Streams.Where(s => s.Id == request.StreamId)
            .Include(s => s.Amplifiers)
            .Include(s => s.FewShots)
            .Include(s => s.AiModel)
            .Include(s => s.StreamFilterTypeCodes)
            .ThenInclude(sf => sf.TypeCode)
            .Include(s => s.StreamMetadataFilters)
            .FirstOrDefaultAsync(cancellationToken: cancellationToken);

        if (stream is null)
        {
            return Result.Fail(new Error($"The stream with id {request.StreamId} was not found",
                new Error("StreamId")));
        }

        var peopleFilters =
            stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.People, true);

        var organisationFilters =
            stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Organisation, true);

        var programmeFilters =
            stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Programme, true);

        var themeFilters =
            stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Theme, true);

        var typeCodeFilters = stream.StreamFilterTypeCodes
            .Where(x => x.IncludeFlag).Select(f => new TypeCode(f.TypeCode.Id, f.TypeCode.Name));

        var filters = new Filters(
            peopleFilters,
            organisationFilters,
            programmeFilters,
            themeFilters,
            typeCodeFilters
        );

        var peopleExclusionFilters =
            stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.People, false);

        var organisationExclusionFilters =
            stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Organisation, false);

        var programmeExclusionFilters =
            stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Programme, false);

        var themeExclusionFilters =
            stream.StreamMetadataFilters.GetFilters(StreamMetadataFilterName.Theme, false);

        var typeCodeExclusionFilters =
            stream.StreamFilterTypeCodes
                .Where(x => !x.IncludeFlag).Select(f => new TypeCode(f.TypeCode.Id, f.TypeCode.Name));

        var exclusionFilters = new ExclusionFilters(
            peopleExclusionFilters,
            organisationExclusionFilters,
            programmeExclusionFilters,
            themeExclusionFilters,
            typeCodeExclusionFilters
        );

        var aiModels = new List<AiModel>();

        if (stream.AiModelId.HasValue)
        {
            aiModels.Add(new AiModel(stream.AiModelId.Value.Value, stream.AiModel.Name));
        }

        var aiParameters = new AiParameters(aiModels, stream.AiModelConfidence);

        var fewShots = new List<FewShot>();
        foreach (var fewShot in stream.FewShots)
        {
            fewShots.Add(new FewShot(fewShot.Id.Value, fewShot.ItemId));
        }

        var advancedSettings = new AdvancedSettings(
            stream.Amplifiers?.Select(a => a.Name),
            stream.Amplifiers?.FirstOrDefault()?.Weight,
            fewShots,
            stream.FewShots?.FirstOrDefault()?.Weight);

        var company = new Company(stream.CompanyId, stream.CompanyName);

        return new Response(
            stream.Id.Value,
            stream.Name,
            stream.UpdatedDateUtc,
            stream.CompanyId is not null ? company : null,
            stream.Prompt,
            stream.AiGeneratedPrompt,
            stream.AiGeneratedPromptId,
            stream.IsMention,
            filters,
            exclusionFilters,
            aiParameters,
            advancedSettings
        );
    }

    #endregion
}
