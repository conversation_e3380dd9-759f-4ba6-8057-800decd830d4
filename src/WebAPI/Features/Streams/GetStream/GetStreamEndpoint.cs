using SharedKernel.Entities;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.Streams.GetStream;
[Route(ApiRoute.StreamRoute)]
public class GetStreamEndpoint : EndpointBaseAsync.WithRequest<Guid>
    .WithResult<ActionResult<Response>>
{
    private readonly ISender _sender;

    public GetStreamEndpoint(ISender sender)
    {
        _sender = sender;
    }


    [HttpGet("{id}")]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Response))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<Response>> HandleAsync([FromRoute] Guid id, CancellationToken cancellationToken = default)
    {
        var queryResult = await _sender.Send(new GetStreamQuery(new StreamId(id)), cancellationToken);

        if (queryResult.IsFailed)
        {
            return NotFound(queryResult.Errors.ToProblemDetails());
        }

        return Ok(queryResult.Value);
    }
}
