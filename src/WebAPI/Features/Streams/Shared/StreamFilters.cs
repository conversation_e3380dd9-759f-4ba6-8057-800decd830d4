namespace WebAPI.Features.Streams.Shared;

public sealed record CreateRequestResponse(
        string Name,
        Guid? CompanyId,
        string Prompt,
        string AiGeneratedPrompt,
        Filters Filters,
        ExclusionFilters ExclusionFilters,
        AiParameters AiParameters,
        AdvancedSettings AdvancedSettings,
        int AiGeneratedPromptId = -1,
        bool IsMention = false
 );

public sealed record UpdateRequestResponse(
    string Name,
    Guid? CompanyId,
    string Prompt,
    string AiGeneratedPrompt,
    Filters Filters,
    ExclusionFilters ExclusionFilters,
    AiParameters AiParameters,
    AdvancedSettings AdvancedSettings,
    int AiGeneratedPromptId = -1
);

public sealed record Filter(Guid Id, string Name);

public sealed record Filters(
    IEnumerable<Filter> People,
    IEnumerable<Filter> Organisations,
    IEnumerable<Filter> Programmes,
    IEnumerable<Filter> Themes,
    IEnumerable<int> TypeCodeIds);

public sealed record ExclusionFilters(
    IEnumerable<Filter> People,
    IEnumerable<Filter> Organisations,
    IEnumerable<Filter> Programmes,
    IEnumerable<Filter> Themes,
    IEnumerable<int> TypeCodeIds);

public sealed record AiParameters(IEnumerable<Guid> AiModelIds, double Confidence);

public sealed record AdvancedSettings(
    IEnumerable<string> Amplifiers,
    double? AmplifierRelevancy,
    IEnumerable<long?> FewShotItemIds,
    double? FewShotRelevancy);

public sealed record Response(
    Guid? Id,
    string Name,
    Guid? CompanyId,
    string Prompt,
    string AiGeneratedPrompt,
    bool IsMention,
    Filters Filters,
    ExclusionFilters ExclusionFilters,
    AiParameters AiParameters,
    AdvancedSettings AdvancedSettings
    );

