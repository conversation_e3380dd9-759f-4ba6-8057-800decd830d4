using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Persistence;

namespace WebAPI.Features.Streams.GetStreams;

public enum StreamSortProperty
{
    Name = 1,
    LastUpdated = 2
}

#region Request Models

public sealed record GetStreamsRequestFilter(
    int PageNumber = 1,
    int PageSize = 25,
    Guid? CompanyId = null,
    Guid? UserId = null,
    string Search = null,
    StreamSortProperty? SortProperty = null,
    PropertySortType? SortType = null
);

public sealed record GetStreamsQuery(
    int PageNumber,
    int PageSize,
    Guid? CompanyId,
    Guid? UserId,
    string Search,
    StreamSortProperty? SortProperty,
    PropertySortType? SortType
) : IQuery<PaginatedQueryResult<Streams>>;

public sealed record Streams
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public int NumberOfAlerts { get; set; }
    public DateTimeOffset LastUpdatedUtc { get; set; }
    public bool IsMention { get; set; }
};

#endregion

#region Handler

internal sealed class GetStreamsQueryHandler : IQueryHandler<GetStreamsQuery, PaginatedQueryResult<Streams>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetStreamsQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<PaginatedQueryResult<Streams>>> Handle(GetStreamsQuery request,
        CancellationToken cancellationToken)
    {
        var searchString = $"%{request.Search?.Trim()}%";

        var streams = from s in _dbContext.Streams
                      join sa in _dbContext.StreamAlerts on s.Id equals sa.StreamId into alerts
                      where (request.CompanyId == null || s.CompanyId == request.CompanyId) && !s.IsFeed &&
                        (request.UserId == null || s.CreatedBy == request.UserId.ToString()) &&
                        (string.IsNullOrWhiteSpace(request.Search) || EF.Functions.Like(s.Name, searchString))
                      select new Streams
                      {
                          Id = s.Id.Value,
                          Name = s.Name,
                          NumberOfAlerts = alerts.Count(),
                          LastUpdatedUtc = s.UpdatedDateUtc,
                          IsMention = s.IsMention
                      };

        if (request.SortProperty.HasValue)
        {
            streams = ApplySorting(streams, request.SortProperty, request.SortType);
        }

        return await PaginatedQueryResult<Streams>.CreateAsync(
            streams, request.PageNumber,
            request.PageSize,
            cancellationToken).ConfigureAwait(false);
    }

    #endregion

    #region Private Helper Methods

    private static IQueryable<Streams> ApplySorting(
        IQueryable<Streams> query,
        StreamSortProperty? sortType,
        PropertySortType? propertySortOrder)
    {
        return sortType switch
        {
            StreamSortProperty.Name => query.OrderBySortOrder(u => u.Name, propertySortOrder),
            StreamSortProperty.LastUpdated => query.OrderBySortOrder(u => u.LastUpdatedUtc, propertySortOrder),
            _ => query.OrderBySortOrder(u => u.Id, PropertySortType.DESC)
        };
    }

    #endregion
}
