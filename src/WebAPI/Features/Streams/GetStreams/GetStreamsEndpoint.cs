using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.Streams.GetStreams;

public class GetStreamsEndpoint : EndpointBaseAsync.WithRequest<GetStreamsRequestFilter>
    .WithResult<ActionResult<PaginatedQueryResult<Streams>>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetStreamsEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }


    [HttpGet(ApiRoute.StreamRoute)]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PaginatedQueryResult<Streams>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public async override Task<ActionResult<PaginatedQueryResult<Streams>>> HandleAsync([FromQuery] GetStreamsRequestFilter request, CancellationToken cancellationToken = default)
    {
        var queryResult = await _sender.Send(new GetStreamsQuery(
            request.PageNumber,
            request.PageSize,
            request.CompanyId,
            request.UserId,
            request.Search,
            request.SortProperty,
            request.SortType));

        var items = _mapper.Map<IEnumerable<Streams>>(queryResult.Value);

        return Ok(new PaginatedResponse<Streams>(
            items,
            request.PageNumber,
            request.PageSize,
            queryResult.Value.TotalRecords));
    }
}
