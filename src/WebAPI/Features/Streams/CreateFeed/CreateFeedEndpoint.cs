using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.Streams.CreateFeed;

[Route(ApiRoute.StreamRoute)]
public class CreateFeedEndpoint : EndpointBaseAsync
    .WithRequest<CreateFeedRequest>
    .WithResult<ActionResult<CreateFeedResponse>>
{
    private readonly ISender _sender;

    public CreateFeedEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpPost("feed")]
    [SwaggerOperation(Tags = [nameof(Streams)])]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(CreateFeedResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<CreateFeedResponse>> HandleAsync([FromBody] CreateFeedRequest request, CancellationToken cancellationToken = default)
    {
        var command = new CreateFeedCommand(request);

        var commandResult = await _sender.Send(command, cancellationToken);

        if (commandResult.IsFailed)
        {
            return Problem(statusCode: StatusCodes.Status400BadRequest, detail: commandResult.Errors[0].Message);
        }

        return Created(string.Empty, commandResult.Value);
    }
}
