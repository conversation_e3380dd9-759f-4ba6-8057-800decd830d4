using SharedKernel.Primitives.Interfaces;

namespace WebAPI.Features.Streams.CreateFeed;

public sealed record CreateFeedCommand(CreateFeedRequest Request) : ICommand<CreateFeedResponse>;

internal sealed class CreateFeedCommandValidator : AbstractValidator<CreateFeedCommand>
{
    public CreateFeedCommandValidator()
    {
        RuleFor(x => x.Request)
            .Must(request => request.People != null ||
                           request.Programmes != null ||
                           request.Themes != null ||
                           request.Organisations != null)
            .WithMessage("At least one of People, Programmes, Themes, or Organisations must be provided.");
    }
}
