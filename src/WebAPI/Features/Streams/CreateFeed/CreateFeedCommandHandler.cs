using Microsoft.EntityFrameworkCore;
using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services;
using WebAPI.Persistence;
using FluentResults;

namespace WebAPI.Features.Streams.CreateFeed;

internal sealed class CreateFeedCommandHandler : ICommandHandler<CreateFeedCommand, CreateFeedResponse>
{
    private const double DefaultConfidence = 0.9;

    private readonly ApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;

    public CreateFeedCommandHandler(
        ApplicationDbContext dbContext,
        ICurrentUserService currentUserService)
    {
        _dbContext = dbContext;
        _currentUserService = currentUserService;
    }

    public async Task<Result<CreateFeedResponse>> Handle(CreateFeedCommand command, CancellationToken cancellationToken)
    {
        var userId = _currentUserService.Id;
        var companyId = _currentUserService.CompanyId;

        if (string.IsNullOrEmpty(userId))
        {
            return Result.Fail(new Error("User ID not found in JWT token", new Error("UserId")));
        }

        // Get default AI model
        var defaultAiModel = await _dbContext.AiModels.FirstOrDefaultAsync(cancellationToken);
        if (defaultAiModel == null)
        {
            return Result.Fail(new Error("No AI model found in the system", new Error("AiModel")));
        }

        // Check if user already has a feed stream
        var existingFeedStream = await _dbContext.Streams
            .Include(s => s.StreamMetadataFilters)
            .FirstOrDefaultAsync(s => s.IsFeed && s.CreatedBy == userId, cancellationToken);

        Stream feedStream;
        bool isUpdate = false;

        if (existingFeedStream != null)
        {
            // Update existing feed stream
            feedStream = existingFeedStream;
            isUpdate = true;

            // Remove existing metadata filters
            _dbContext.StreamMetadataFilter.RemoveRange(feedStream.StreamMetadataFilters);
        }
        else
        {
            // Create new feed stream
            var streamName = $"User Feed - {userId}";

            feedStream = Stream.Create(
                companyId: new Guid(companyId),
                aiModelId: defaultAiModel.Id,
                name: streamName,
                prompt: string.Empty, // No prompt for feed streams
                companyName: string.Empty,
                companySpecificStream: null,
                aiGeneratedPrompt: string.Empty,
                aiGeneratedPromptId: -1,
                aiModelConfidence: DefaultConfidence,
                isFeed: true
            );

            await _dbContext.Streams.AddAsync(feedStream, cancellationToken);
        }

        // Create metadata filters from the request
        var metadataFilters = CreateMetadataFilters(command.Request, feedStream.Id);

        if (metadataFilters.Count > 0)
        {
            await _dbContext.StreamMetadataFilter.AddRangeAsync(metadataFilters, cancellationToken);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        var message = isUpdate ? "Feed stream updated successfully" : "Feed stream created successfully";

        return new CreateFeedResponse(feedStream.Id.Value, message);
    }

    private static List<StreamMetadataFilter> CreateMetadataFilters(CreateFeedRequest request, StreamId streamId)
    {
        var filters = new List<StreamMetadataFilter>();

        // Add People filters
        foreach (var person in request.People ?? Enumerable.Empty<string>())
        {
            if (!string.IsNullOrWhiteSpace(person))
            {
                filters.Add(StreamMetadataFilter.Create(streamId, StreamMetadataFilterName.People, true, person));
            }
        }

        // Add Programme filters
        foreach (var programme in request.Programmes ?? Enumerable.Empty<string>())
        {
            if (!string.IsNullOrWhiteSpace(programme))
            {
                filters.Add(StreamMetadataFilter.Create(streamId, StreamMetadataFilterName.Programme, true, programme));
            }
        }

        // Add Theme filters
        foreach (var theme in request.Themes ?? Enumerable.Empty<string>())
        {
            if (!string.IsNullOrWhiteSpace(theme))
            {
                filters.Add(StreamMetadataFilter.Create(streamId, StreamMetadataFilterName.Theme, true, theme));
            }
        }

        // Add Organisation filters
        foreach (var organisation in request.Organisations ?? Enumerable.Empty<string>())
        {
            if (!string.IsNullOrWhiteSpace(organisation))
            {
                filters.Add(StreamMetadataFilter.Create(streamId, StreamMetadataFilterName.Organisation, true, organisation));
            }
        }

        return filters;
    }
}
