using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Persistence;

namespace WebAPI.Features.Streams.GetIsMentionStream;

#region Request Models

public sealed record GetIsMentionStreamQuery(StreamId StreamId) : IQuery<Response>;

public sealed record Response(bool IsMention);

internal sealed class GetIsMentionStreamQueryValidator : AbstractValidator<GetIsMentionStreamQuery>
{
    public GetIsMentionStreamQueryValidator()
    {
        RuleFor(x => x.StreamId).NotEmpty();
    }
}

#endregion

#region Handler

internal sealed class GetIsMentionStreamQueryHandler : IQueryHandler<GetIsMentionStreamQuery, Response>
{
    private readonly ApplicationDbContext _dbContext;

    public GetIsMentionStreamQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<Response>> Handle(GetIsMentionStreamQuery request, CancellationToken cancellationToken)
    {
        var stream = await _dbContext.Streams.Where(s => s.Id == request.StreamId)
            .Select(x => new { x.Id, x.IsMention }).FirstOrDefaultAsync(cancellationToken);

        if (stream is null)
        {
            return Result.Fail(new Error($"The stream with id {request.StreamId} was not found",
                new Error("StreamId")));
        }

        return new Response(stream.IsMention);
    }

    #endregion
}
