using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Persistence;

namespace WebAPI.Features.Streams.GetIsFeedStream;

#region Request Models

public sealed record GetIsFeedStreamQuery(StreamId StreamId) : IQuery<Response>;

public sealed record Response(bool IsFeed);

internal sealed class GetIsFeedStreamQueryValidator : AbstractValidator<GetIsFeedStreamQuery>
{
    public GetIsFeedStreamQueryValidator()
    {
        RuleFor(x => x.StreamId).NotEmpty();
    }
}

#endregion

#region Handler

internal sealed class GetIsFeedStreamQueryHandler : IQueryHandler<GetIsFeedStreamQuery, Response>
{
    private readonly ApplicationDbContext _dbContext;

    public GetIsFeedStreamQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<Response>> Handle(GetIsFeedStreamQuery request, CancellationToken cancellationToken)
    {
        var stream = await _dbContext.Streams.Where(s => s.Id == request.StreamId)
            .Select(x => new { x.Id, x.IsFeed }).FirstOrDefaultAsync(cancellationToken);

        if (stream is null)
        {
            return Result.Fail(new Error($"The stream with id {request.StreamId} was not found",
                new Error("StreamId")));
        }

        return new Response(stream.IsFeed);
    }

    #endregion
}
