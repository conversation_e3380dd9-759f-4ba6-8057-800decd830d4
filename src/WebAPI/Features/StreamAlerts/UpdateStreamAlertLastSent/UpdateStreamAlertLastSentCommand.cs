using SharedKernel.Entities;

namespace WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;

using SharedKernel.Primitives.Interfaces;

public sealed class UpdateStreamAlertLastSentCommand : ICommand<StreamAlert>
{
    public UpdateStreamAlertLastSentCommand(Guid streamAlertId,
        DateTimeOffset lastSent,
        string recipients = "",
        int numberOfItems = 0)
    {
        StreamAlertId = new StreamAlertId(streamAlertId);
        LastSent = lastSent;
        Recipients = recipients;
        NumberOfItems = numberOfItems;
    }

    public StreamAlertId StreamAlertId { get; }
    public DateTimeOffset LastSent { get; }
    public string Recipients { get; set; }
    public int NumberOfItems { get; set; }
}
