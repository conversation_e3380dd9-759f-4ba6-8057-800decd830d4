using SharedKernel.Entities;
using SharedKernel.Services;

namespace WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;

public interface IAlertCalculator
{
    DateTimeOffset? CalculateNextAlertDate(
        DateTimeOffset startDateTime,
        DateTimeOffset? endDateTime,
        StreamAlertFrequency frequency);

    DateTimeOffset? CalculateLastSentAlertDate(
        DateTimeOffset startDateTime,
        DateTimeOffset? endDateTime,
        StreamAlertFrequency frequency);
}

public sealed class AlertCalculator : IAlertCalculator
{

    private readonly IDateTimeService _dateTimeService;

    public AlertCalculator(IDateTimeService dateTimeService)
    {
        _dateTimeService = dateTimeService;
    }

    public DateTimeOffset? CalculateNextAlertDate(
        DateTimeOffset startDateTime,
        DateTimeOffset? endDateTime,
        StreamAlertFrequency frequency)
    {
        DateTime currentDateTime = _dateTimeService.UtcNow.UtcDateTime;

        // If the current date-time is before the starting date, return the starting date
        if (currentDateTime < startDateTime)
        {
            return startDateTime;
        }

        // If the end date-time is set and current date-time is past the end date, no alert should be scheduled
        if (endDateTime.HasValue && currentDateTime >= endDateTime.Value)
        {
            return null;
        }

        // Calculate the next alert time based on frequency
        DateTimeOffset nextAlertDate = startDateTime;

        switch (frequency)
        {
            case StreamAlertFrequency.None:
                // No alert if frequency is None
                return null;

            case StreamAlertFrequency.Hour:
                // Hourly alerts: Runs only between 6:00AM - 8:00PM (Monday to Friday)
                while (nextAlertDate <= currentDateTime ||
                       !IsValidHourlyAlertTime(nextAlertDate))
                {
                    nextAlertDate = nextAlertDate.AddHours(1);
                }
                break;

            case StreamAlertFrequency.Day:
                // Daily alerts: Runs only Monday to Friday
                while (nextAlertDate <= currentDateTime ||
                       !IsWeekday(nextAlertDate))
                {
                    nextAlertDate = nextAlertDate.AddDays(1);
                }
                break;

            case StreamAlertFrequency.Week:
                // Weekly alerts: Same as before, add weeks until the next alert time is in the future
                while (nextAlertDate <= currentDateTime)
                {
                    nextAlertDate = nextAlertDate.AddDays(7);
                }
                break;

            case StreamAlertFrequency.Month:
                // Monthly alerts: If scheduled day is a weekend, reschedule to the following Monday
                while (nextAlertDate <= currentDateTime)
                {
                    nextAlertDate = nextAlertDate.AddMonths(1);
                    if (!IsWeekday(nextAlertDate))
                    {
                        nextAlertDate = MoveToNextMonday(nextAlertDate);
                    }
                }
                break;

            default:
                throw new ArgumentException("Invalid frequency value provided.");
        }

        return nextAlertDate;
    }

    public DateTimeOffset? CalculateLastSentAlertDate(
        DateTimeOffset startDateTime,
        DateTimeOffset? endDateTime,
        StreamAlertFrequency frequency)
    {
        DateTime currentDateTime = _dateTimeService.UtcNow.UtcDateTime;

        // Return null if the start date is after the end date (no valid alerts)
        if (endDateTime.HasValue && startDateTime > endDateTime.Value)
        {
            return null;
        }

        // Return null if the current date is before the start date
        if (currentDateTime < startDateTime)
        {
            return null;
        }

        // Set the last alert date initially to the start date
        DateTimeOffset lastAlertDate = startDateTime;

        switch (frequency)
        {
            case StreamAlertFrequency.None:
                // No alerts to send when frequency is None
                return null;

            case StreamAlertFrequency.Hour:
                // Hourly Alerts: Only between 6:00AM - 8:00PM, Monday to Friday
                while (lastAlertDate.AddHours(1) <= currentDateTime)
                {
                    lastAlertDate = lastAlertDate.AddHours(1);
                    if (!IsValidHourlyAlertTime(lastAlertDate))
                    {
                        continue;
                    }
                }
                break;

            case StreamAlertFrequency.Day:
                // Daily Alerts: Only Monday to Friday
                while (lastAlertDate.AddDays(1) <= currentDateTime)
                {
                    lastAlertDate = lastAlertDate.AddDays(1);
                    if (!IsWeekday(lastAlertDate))
                    {
                        continue;
                    }
                }
                break;

            case StreamAlertFrequency.Week:
                // Weekly Alerts: Add weeks until reaching the current time
                while (lastAlertDate.AddDays(7) <= currentDateTime)
                {
                    lastAlertDate = lastAlertDate.AddDays(7);
                }
                break;

            case StreamAlertFrequency.Month:
                // Monthly Alerts: Move to the last valid date before the current date
                while (lastAlertDate.AddMonths(1) <= currentDateTime)
                {
                    lastAlertDate = lastAlertDate.AddMonths(1);
                    if (!IsWeekday(lastAlertDate))
                    {
                        lastAlertDate = MoveToPreviousFriday(lastAlertDate);
                    }
                }
                break;

            default:
                throw new ArgumentException("Invalid frequency value provided.");
        }

        // If there's an end date and the calculated last alert is past it, return null
        if (endDateTime.HasValue && lastAlertDate > endDateTime.Value)
        {
            return null;
        }

        return lastAlertDate;
    }

    private static bool IsWeekday(DateTimeOffset date)
    {
        return date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday;
    }

    private static bool IsValidHourlyAlertTime(DateTimeOffset date)
    {
        // Only run between 6:00AM and 8:00PM, Monday to Friday
        return IsWeekday(date) && date.TimeOfDay >= TimeSpan.FromHours(6) && date.TimeOfDay <= TimeSpan.FromHours(20);
    }

    private static DateTimeOffset MoveToNextMonday(DateTimeOffset date)
    {
        int daysToMonday = ((int)DayOfWeek.Monday - (int)date.DayOfWeek + 7) % 7;
        return date.AddDays(daysToMonday);
    }

    private static DateTimeOffset MoveToPreviousFriday(DateTimeOffset date)
    {
        int daysBackToFriday = ((int)date.DayOfWeek - (int)DayOfWeek.Friday + 7) % 7;
        return date.AddDays(-daysBackToFriday);
    }
}
