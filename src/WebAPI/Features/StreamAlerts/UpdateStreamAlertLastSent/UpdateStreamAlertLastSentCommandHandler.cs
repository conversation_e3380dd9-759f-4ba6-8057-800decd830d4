using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Persistence;

namespace WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;

internal sealed class
    UpdateStreamAlertLastSentCommandHandler : ICommandHandler<UpdateStreamAlertLastSentCommand, StreamAlert>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IAlertCalculator _alertCalculator;
    private readonly ILogger<UpdateStreamAlertLastSentCommandHandler> _logger;

    public UpdateStreamAlertLastSentCommandHandler(ApplicationDbContext dbContext,
        IAlertCalculator alertCalculator,
        ILogger<UpdateStreamAlertLastSentCommandHandler> logger)
    {
        _dbContext = dbContext;
        _alertCalculator = alertCalculator;
        _logger = logger;
    }

    public async Task<Result<StreamAlert>> Handle(UpdateStreamAlertLastSentCommand request,
        CancellationToken cancellationToken)
    {
        var streamAlert = await _dbContext.StreamAlerts.FindAsync([request.StreamAlertId], cancellationToken);

        if (streamAlert is null)
        {
            return Result.Fail(new Error($"Stream alert with ID {request.StreamAlertId.Value} was not found"));
        }


        // Update just the necessary fields
        streamAlert.UpdateLastSent(request.LastSent);

        var nextAlertDate = _alertCalculator.CalculateNextAlertDate(streamAlert.StartDateTime, streamAlert.EndDateTime,
            streamAlert.Frequency);

        streamAlert.UpdateNextAlertDate(nextAlertDate);

        _dbContext.StreamAlerts.Update(streamAlert);
        await _dbContext.StreamAlertLogs.AddAsync(
            new StreamAlertLog
            {
                Id = Guid.NewGuid(),
                StreamAlertId = request.StreamAlertId.Value,
                SentDate = request.LastSent,
                Recipients = request.Recipients,
                NumberOfItems = request.NumberOfItems
            }, cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated LastSent for StreamAlert with ID: {StreamAlertId}", request.StreamAlertId);

        return Result.Ok(streamAlert);
    }
}
