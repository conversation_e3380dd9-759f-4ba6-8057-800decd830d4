namespace WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;

public sealed record UpdateStreamAlertLastSentRequest(
    Guid StreamAlertId,
    DateTimeOffset? LastSent,
    string Recipients,
    int NumberOfItems);

internal sealed class UpdateStreamAlertLastSentRequestValidator : AbstractValidator<UpdateStreamAlertLastSentRequest>
{
    public UpdateStreamAlertLastSentRequestValidator()
    {
        RuleFor(x => x.StreamAlertId).NotEmpty();

        When(x => x.LastSent.HasValue, () =>
        {
            RuleFor(x => x.LastSent)
                .LessThanOrEqualTo(DateTimeOffset.UtcNow).WithMessage("LastSent cannot be in the future.");
        });
    }
}
