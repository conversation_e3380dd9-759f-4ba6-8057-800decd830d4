using WebAPI.Features.StreamAlerts.UpdateStreamAlert;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;

public class UpdateStreamAlertLastSentEndpoint : EndpointBaseAsync
    .WithRequest<UpdateStreamAlertLastSentRequest>
    .WithResult<ActionResult<UpdateStreamAlertResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public UpdateStreamAlertLastSentEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpPut($"{ApiRoute.StreamAlertRoute}/last-sent")]
    [SwaggerOperation(Tags = [nameof(StreamAlerts)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(UpdateStreamAlertResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<UpdateStreamAlertResponse>> HandleAsync([FromBody] UpdateStreamAlertLastSentRequest request, CancellationToken cancellationToken = new())
    {
        if (!request.LastSent.HasValue)
        {
            request = request with { LastSent = DateTimeOffset.UtcNow };
        }

        var commandResult = await _sender.Send(
            new UpdateStreamAlertLastSentCommand(
                request.StreamAlertId,
                request.LastSent.Value,
                request.Recipients,
                request.NumberOfItems), cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Ok(_mapper.Map<UpdateStreamAlertResponse>(commandResult.Value));
    }
}
