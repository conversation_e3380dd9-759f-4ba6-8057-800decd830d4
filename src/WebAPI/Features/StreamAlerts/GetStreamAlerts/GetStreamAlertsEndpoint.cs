using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.StreamAlerts.GetStreamAlerts;

public class GetStreamAlertsEndpoint : EndpointBaseAsync
    .WithRequest<GetStreamAlertsRequestFilter>
    .WithResult<ActionResult<PaginatedQueryResult<GetStreamAlertsResponse>>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetStreamAlertsEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpGet(ApiRoute.StreamAlertRoute)]
    [SwaggerOperation(Tags = [nameof(StreamAlerts)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IEnumerable<GetStreamAlertsResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<PaginatedQueryResult<GetStreamAlertsResponse>>> HandleAsync([FromQuery] GetStreamAlertsRequestFilter request, CancellationToken cancellationToken = new CancellationToken())
    {
        var queryResult = await _sender.Send(new GetStreamAlertsQuery(request.PageNumber,
            request.PageSize,
            request.Search,
            request.StreamId,
            request.SortProperty,
            request.SortType), cancellationToken);

        // Check if the result is successful
        if (queryResult.IsFailed)
        {
            // Log or handle errors
            var errorMessages = queryResult.Errors.Select(e => e.Message).ToList();

            // Return an appropriate response (e.g., BadRequest with error details)
            return BadRequest(new
            {
                Message = "Failed to fetch stream alerts.",
                Errors = errorMessages
            });
        }

        // Proceed if successful
        var items = _mapper.Map<PaginatedResponse<GetStreamAlertsResponse>>(queryResult.Value);
        return Ok(items);
    }
}

