using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;

namespace WebAPI.Features.StreamAlerts.GetStreamAlerts;

public sealed record GetStreamAlertsRequestFilter(
    int PageNumber = 1,
    int PageSize = 25,
    string Search = null,
    string StreamId = null,
    StreamAlertSortProperty? SortProperty = null,
    PropertySortType? SortType = null
);

public sealed record GetStreamAlertsQuery(
    int PageNumber,
    int PageSize,
    string Search,
    string StreamId,
    StreamAlertSortProperty? SortProperty,
    PropertySortType? SortType) : IQuery<PaginatedResponse<GetStreamAlertsResponse>>;
