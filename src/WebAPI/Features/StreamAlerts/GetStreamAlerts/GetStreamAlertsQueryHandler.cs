using SharedKernel.Entities;
using SharedKernel.Primitives.Contracts;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services.NotificationApi;
using SharedKernel.Services.UserApi;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Persistence;

namespace WebAPI.Features.StreamAlerts.GetStreamAlerts;

internal sealed class GetStreamAlertsQueryHandler : IQueryHandler<GetStreamAlertsQuery, PaginatedResponse<GetStreamAlertsResponse>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly INotificationApiClient _notificationApiClient;
    private readonly IUserApiClient _userApiClient;

    public GetStreamAlertsQueryHandler(ApplicationDbContext dbContext, INotificationApiClient notificationApiClient, IUserApiClient userApiClient)
    {
        _dbContext = dbContext;
        _notificationApiClient = notificationApiClient;
        _userApiClient = userApiClient;
    }

    public async Task<Result<PaginatedResponse<GetStreamAlertsResponse>>> Handle(GetStreamAlertsQuery request, CancellationToken cancellationToken)
    {
        var searchString = $"%{request.Search?.Trim()}%";

        IQueryable<StreamAlert> query = _dbContext.StreamAlerts.Include(s => s.Stream)
            .Where(s => string.IsNullOrWhiteSpace(request.Search)
                        || EF.Functions.Like(s.Name, searchString))
            .OrderBy(x => x.Id);

        query = string.IsNullOrEmpty(request.StreamId)
            ? query
            : query.Where(s => s.StreamId == new StreamId(new Guid(request.StreamId)));

        if (request.SortProperty.HasValue)
        {
            query = ApplySorting(query, request.SortProperty, request.SortType);
        }

        // Get the total count of records (before pagination)
        var totalRecords = await query.CountAsync(cancellationToken);

        // Apply pagination
        var streamAlerts = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(streamAlert => new GetStreamAlertsResponse
            {
                Id = streamAlert.Id.Value,
                StreamId = streamAlert.StreamId.Value,
                StreamName = streamAlert.Stream.Name,
                CompanyName = streamAlert.Stream.CompanyName,
                CreatedByUserId = streamAlert.CreatedBy,
                Name = streamAlert.Name,
                Status = streamAlert.Status,
                StartDateTime = streamAlert.StartDateTime,
                EndDateTime = streamAlert.EndDateTime,
                EmailRecipients = streamAlert.EmailRecipients,
                Frequency = streamAlert.Frequency,
                Recurrence = streamAlert.Recurrence,
                DistributionGroupId = streamAlert.DistributionGroupId,
                LastSent = streamAlert.LastSent,
                NextAlertDate = streamAlert.NextAlertDate
            })
            .ToListAsync(cancellationToken);

        // Extract unique DistributionGroupIds to minimize calls
        var distributionGroupIds = streamAlerts
            .Where(sa => sa.DistributionGroupId.HasValue)
            .Select(sa => sa.DistributionGroupId.Value)
            .Distinct()
            .ToList();

        Result<List<DistributionGroupDto>> distributionGroupResult = new Result<List<DistributionGroupDto>>();
        if (distributionGroupIds.Count > 0)
        {
            // Batch query Notification Service for Distribution Group details
            distributionGroupResult = await _notificationApiClient.GetDistributionGroupsAsync(distributionGroupIds, cancellationToken);

            if (!distributionGroupResult.IsSuccess)
            {
                Console.WriteLine($"Failed to fetch distribution groups: {distributionGroupResult.Errors}");
                return Result.Fail($"Failed to fetch distribution groups: {distributionGroupResult.Errors}");
            }
        }

        // Extract unique userIds to minimize calls
        var userIds = streamAlerts
            .Select(sa => sa.CreatedByUserId)
            .Distinct()
            .ToList();

        Result<List<UserDto>> usersResult = new Result<List<UserDto>>();
        if (userIds.Count > 0)
        {
            usersResult = await _userApiClient.GetUserByIds(userIds, cancellationToken);

            if (!usersResult.IsSuccess)
            {
                Console.WriteLine($"Failed to fetch users: {usersResult.Errors}");
                return Result.Fail($"Failed to fetch users: {usersResult.Errors}");
            }
        }

        // Build the response
        var items = streamAlerts.Select(streamAlert =>
        {
            DistributionGroupDto distributionGroup = null;
            if (distributionGroupIds.Count > 0)
            {
                distributionGroup = distributionGroupResult.Value.FirstOrDefault(dg => streamAlert.DistributionGroupId != null
                    && dg.Id == streamAlert.DistributionGroupId.Value);
            }

            UserDto userDto = null;
            if (userIds.Count > 0)
            {
                userDto = usersResult.Value.FirstOrDefault(user => user.Id == new Guid(streamAlert.CreatedByUserId));
            }

            int numberOfEmailRecipients = 0;
            // Check if the string is not null or empty
            if (!string.IsNullOrWhiteSpace(streamAlert.EmailRecipients))
            {
                // Split by comma and count non-empty items after trimming whitespace
                numberOfEmailRecipients = streamAlert.EmailRecipients
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(email => email.Trim())
                    .Count();
            }

            return new GetStreamAlertsResponse
            {
                Id = streamAlert.Id,
                StreamId = streamAlert.StreamId,
                StreamName = streamAlert.StreamName,
                CompanyName = streamAlert.CompanyName,
                Name = streamAlert.Name,
                Status = streamAlert.Status,
                StartDateTime = streamAlert.StartDateTime,
                EndDateTime = streamAlert.EndDateTime,
                Frequency = streamAlert.Frequency,
                Recurrence = streamAlert.Recurrence,
                NumberOfRecipients = distributionGroup?.TotalUsers + numberOfEmailRecipients ?? numberOfEmailRecipients,
                DistributionGroupId = streamAlert.DistributionGroupId,
                DistributionGroupName = distributionGroup?.Name ?? "",
                LastSent = streamAlert.LastSent,
                NextAlertDate = streamAlert.NextAlertDate,
                CreatedBy = userDto
            };
        }).ToList();

        // Return Paginated Response with TotalRecords
        return new PaginatedResponse<GetStreamAlertsResponse>(
            items,
            request.PageNumber,
            request.PageSize,
            totalRecords
        );
    }

    private static IQueryable<StreamAlert> ApplySorting(
        IQueryable<StreamAlert> query,
        StreamAlertSortProperty? sortType,
        PropertySortType? propertySortOrder)
    {
        return sortType switch
        {
            StreamAlertSortProperty.Name => query.OrderBySortOrder(u => u.Name, propertySortOrder),
            StreamAlertSortProperty.LastSent => query.OrderBySortOrder(u => u.LastSent, propertySortOrder),
            StreamAlertSortProperty.NextAlertDate => query.OrderBySortOrder(u => u.NextAlertDate, propertySortOrder),
            _ => query.OrderBySortOrder(u => u.Id, PropertySortType.DESC)
        };
    }
}

public enum StreamAlertSortProperty
{
    Name = 1,
    Recipients = 2,
    CreatedBy = 3,
    LastSent = 4,
    NextAlertDate = 5
}

public class User
{
    public string Id { get; set; }
    public string Email { get; set; }
    public string FirstName { get; set; }
    public string Surname { get; set; }
    public string JobTitle { get; set; }
    public bool IsInternal { get; set; }
}
