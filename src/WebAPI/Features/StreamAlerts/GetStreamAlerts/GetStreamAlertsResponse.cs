using System.Text.Json.Serialization;
using SharedKernel.Entities;
using SharedKernel.Primitives.Contracts;
using WebAPI.Infrastructure.Mappers;

namespace WebAPI.Features.StreamAlerts.GetStreamAlerts;

public record GetStreamAlertsResponse : IMapFrom<StreamAlert>
{
    public Guid Id { get; init; }
    public Guid StreamId { get; init; }
    public StreamAlertFrequency Frequency { get; init; }
    public StreamAlertRecurrence Recurrence { get; init; }
    public DateTimeOffset? LastSent { get; init; }
    public DateTimeOffset StartDateTime { get; init; }
    public DateTimeOffset? EndDateTime { get; init; }
    public DateTimeOffset? NextAlertDate { get; init; }
    public StreamAlertStatus Status { get; init; }
    public string HangfireId { get; init; }
    public string Name { get; init; }
    public string StreamName { get; init; }

    public string CompanyName { get; init; }

    [JsonIgnore]
    public string CreatedByUserId { get; init; }
    public string CreatedByName { get; init; }
    public string DistributionGroupName { get; set; }
    public string EmailRecipients { get; set; } // Stored as comma-separated emails
    public int NumberOfRecipients { get; init; }
    public Guid? DistributionGroupId { get; init; }
    public UserDto CreatedBy { get; init; }

    public void Mapping(Profile profile)
    {
        profile.CreateMap<StreamAlert, GetStreamAlertsResponse>()
            .ForMember(destination => destination.Id,
                source => source.MapFrom(s => s.Id.Value))
            .ForMember(destination => destination.StreamId,
                source => source.MapFrom(s => s.StreamId.Value));
    }
}
