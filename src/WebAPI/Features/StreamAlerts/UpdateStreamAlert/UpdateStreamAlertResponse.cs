using SharedKernel.Entities;
using WebAPI.Infrastructure.Mappers;

namespace WebAPI.Features.StreamAlerts.UpdateStreamAlert;

public sealed record UpdateStreamAlertResponse : IMapFrom<StreamAlert>
{
    public Guid Id { get; init; }
    public Guid StreamId { get; init; }
    public StreamAlertFrequency Frequency { get; init; }
    public DateTimeOffset StartDateTime { get; init; }
    public DateTimeOffset? EndDateTime { get; init; }
    public StreamAlertStatus Status { get; init; }
    public string HangfireId { get; init; }
    public string Name { get; init; }
    public Guid? DistributionGroupId { get; init; }

    public void Mapping(Profile profile)
    {
        profile.CreateMap<StreamAlert, UpdateStreamAlertResponse>()
            .ForMember(destination => destination.Id,
                source => source.MapFrom(s => s.Id.Value))
            .ForMember(destination => destination.StreamId,
                source => source.MapFrom(s => s.StreamId.Value))
            ;
    }
}
