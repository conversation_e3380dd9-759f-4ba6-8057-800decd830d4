using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Features.StreamAlerts.HangfireApi;
using WebAPI.Features.StreamAlerts.HangfireApi.UpdateScheduleStreamAlertJob;
using WebAPI.Persistence;

namespace WebAPI.Features.StreamAlerts.UpdateStreamAlert;

internal sealed class UpdateStreamAlertCommandHandler : ICommandHandler<UpdateStreamAlertCommand, StreamAlert>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IHangfireApi _hangfireApi;
    private readonly ILogger<UpdateStreamAlertCommandHandler> _logger;

    public UpdateStreamAlertCommandHandler(
        ApplicationDbContext dbContext,
        IHangfireApi hangfireApi,
        ILogger<UpdateStreamAlertCommandHandler> logger)
    {
        _dbContext = dbContext;
        _hangfireApi = hangfireApi;
        _logger = logger;
    }

    public async Task<Result<StreamAlert>> Handle(UpdateStreamAlertCommand request, CancellationToken cancellationToken)
    {
        var streamAlert = await _dbContext.StreamAlerts.FindAsync([request.StreamAlertId], cancellationToken);

        if (streamAlert is null)
        {
            return Result.Fail(new Error($"The stream alert with id {request.StreamAlertId.Value} was not found",
                new Error("StreamAlertId")));
        }

        var hangfireApiResult = await _hangfireApi.UpdateScheduleStreamAlertJob(streamAlert.HangfireId,
            new UpdateScheduleStreamAlertJobRequest(
                streamAlert.Id.Value,
                streamAlert.Frequency,
                request.StartDateTime,
                request.EndDateTime), cancellationToken);

        if (hangfireApiResult.IsSuccess)
        {
            streamAlert.Update(request.StartDateTime, request.EndDateTime, request.TemplateId, request.Name,
                request.RecipientEmails, request.DistributionGroupId);

            if (!string.IsNullOrEmpty(hangfireApiResult.Value.JobId))
            {
                //we need to update HangfireId as it will change every time we run an update and change settings
                streamAlert.UpdateHangfireId(hangfireApiResult.Value.JobId);
            }
        }
        else
        {
            _logger.LogError("Failed to access UpdateScheduleStreamAlertJob from the HangfireApi API service");

            // Extract the first error message, if available
            var firstErrorMessage = hangfireApiResult.Errors.FirstOrDefault()?.Message;

            // If the error contains a "not found" message, return 404
            if (!string.IsNullOrEmpty(firstErrorMessage) &&
                firstErrorMessage.Contains("not found", StringComparison.OrdinalIgnoreCase))
            {
                return Result.Fail(new Error(firstErrorMessage).WithMetadata("StatusCode", 404));
            }

            // For other failures, return a generic error
            return Result.Fail(new Error("An error occurred while updating the Stream Alert Job.")
                .WithMetadata("StatusCode", 400));
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        return streamAlert;
    }
}
