namespace WebAPI.Features.StreamAlerts.UpdateStreamAlert;

public sealed record UpdateStreamAlertRequest(
    DateTimeOffset StartDateTime,
    DateTimeOffset? EndDateTime,
    Guid? TemplateId,
    string Name,
    List<string> RecipientEmails,
    Guid? DistributionGroupId);

internal sealed class UpdateStreamAlertRequestValidator : AbstractValidator<UpdateStreamAlertRequest>
{
    public UpdateStreamAlertRequestValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.StartDateTime)
            .NotEmpty().WithMessage("StartDateTime is required.");
        RuleFor(x => x.EndDateTime)
            .GreaterThan(x => x.StartDateTime).When(x => x.EndDateTime.HasValue)
            .WithMessage("EndDateTime must be later than StartDateTime.");
    }
}
