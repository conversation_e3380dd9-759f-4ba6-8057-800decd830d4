using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;

namespace WebAPI.Features.StreamAlerts.UpdateStreamAlert;

public sealed record UpdateStreamAlertCommand(
    StreamAlertId StreamAlertId,
        DateTimeOffset StartDateTime,
        DateTimeOffset? EndDateTime,
        Guid? TemplateId,
        string Name,
        List<string> RecipientEmails,
        Guid? DistributionGroupId
    ) : ICommand<StreamAlert>;

internal sealed class UpdateStreamAlertCommandValidator : AbstractValidator<UpdateStreamAlertCommand>
{
    public UpdateStreamAlertCommandValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.StreamAlertId.Value).NotEmpty();
        RuleFor(x => x.StartDateTime)
            .NotEmpty();
        RuleFor(x => x.EndDateTime)
            .GreaterThan(x => x.StartDateTime)
            .When(x => x.EndDateTime.HasValue);

        RuleFor(x => x)
            .Must(x => x.DistributionGroupId.HasValue || x.RecipientEmails is { Count: > 0 })
            .WithMessage("Either DistributionGroupId must be provided or at least one RecipientEmail is required.");
    }
}
