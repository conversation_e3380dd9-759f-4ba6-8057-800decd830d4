using SharedKernel.Entities;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.StreamAlerts.UpdateStreamAlert;

public class UpdateStreamAlertEndpoint : EndpointBaseAsync
    .WithRequest<UpdateStreamAlertRequestWrapper>
    .WithResult<ActionResult<UpdateStreamAlertResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public UpdateStreamAlertEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpPut($"{ApiRoute.StreamAlertRoute}/{{id}}")]
    [SwaggerOperation(Tags = [nameof(StreamAlerts)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(UpdateStreamAlertResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<UpdateStreamAlertResponse>> HandleAsync([FromRoute] UpdateStreamAlertRequestWrapper requestWrapper, CancellationToken cancellationToken = new())
    {
        var commandResult = await _sender.Send(
            new UpdateStreamAlertCommand(
                new StreamAlertId(requestWrapper.Id),
                requestWrapper.Request.StartDateTime,
                requestWrapper.Request.EndDateTime,
                requestWrapper.Request.TemplateId,
                requestWrapper.Request.Name,
                requestWrapper.Request.RecipientEmails,
                requestWrapper.Request.DistributionGroupId),
            cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Ok(_mapper.Map<UpdateStreamAlertResponse>(commandResult.Value));
    }
}
