using SharedKernel.Entities;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.StreamAlerts.DeleteStreamAlert;

public class DeleteStreamAlertEndpoint : EndpointBaseAsync
    .WithRequest<Guid>
    .WithoutResult
{
    private readonly ISender _sender;

    public DeleteStreamAlertEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpDelete($"{ApiRoute.StreamAlertRoute}/{{id}}")]
    [SwaggerOperation(Tags = [nameof(StreamAlerts)])]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult> HandleAsync([FromRoute] Guid id, CancellationToken cancellationToken = new())
    {
        var commandResult = await _sender.Send(new DeleteStreamAlertCommand(new StreamAlertId(id)), cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return NoContent();
    }
}
