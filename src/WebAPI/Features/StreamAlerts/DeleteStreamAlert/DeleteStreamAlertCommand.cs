using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;

namespace WebAPI.Features.StreamAlerts.DeleteStreamAlert;

public sealed record DeleteStreamAlertCommand(StreamAlertId Id) : ICommand;

internal sealed class DeleteStreamAlertCommandValidator : AbstractValidator<DeleteStreamAlertCommand>
{
    public DeleteStreamAlertCommandValidator()
    {
        RuleFor(x => x.Id.Value).NotEmpty();
    }
}
