using SharedKernel.Primitives.Interfaces;
using WebAPI.Features.StreamAlerts.HangfireApi;
using WebAPI.Persistence;

namespace WebAPI.Features.StreamAlerts.DeleteStreamAlert;

internal sealed class DeleteStreamAlertCommandHandler : ICommandHandler<DeleteStreamAlertCommand>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IHangfireApi _hangfireApi;
    private readonly ILogger<DeleteStreamAlertCommandHandler> _logger;

    public DeleteStreamAlertCommandHandler(
        ApplicationDbContext dbContext,
        IHangfireApi hangfireApi,
        ILogger<DeleteStreamAlertCommandHandler> logger)
    {
        _dbContext = dbContext;
        _hangfireApi = hangfireApi;
        _logger = logger;
    }

    public async Task<Result> Handle(DeleteStreamAlertCommand request, CancellationToken cancellationToken)
    {
        var streamAlert = await _dbContext.StreamAlerts.FindAsync([request.Id], cancellationToken);

        if (streamAlert is null)
        {
            return Result.Fail(new Error($"The Stream Alert with {request.Id.Value} was not found", new Error("StreamAlertId")));
        }

        var hangfireApiResult = await _hangfireApi.DeleteScheduleStreamAlertJob(streamAlert.HangfireId, cancellationToken);

        if (hangfireApiResult.IsFailed)
        {
            _logger.LogError("Failed to access DeleteScheduleStreamAlertJob from the HangfireApi API service");

            return Result.Fail(hangfireApiResult.Errors);
        }

        _dbContext.StreamAlerts.Remove(streamAlert);

        await _dbContext.SaveChangesAsync();

        return Result.Ok();
    }

}
