using System.Text.Json.Serialization;
using SharedKernel.Entities;

namespace WebAPI.Features.StreamAlerts.HangfireApi.UpdateScheduleStreamAlertJob;

public sealed record UpdateScheduleStreamAlertJobRequest
{
    public UpdateScheduleStreamAlertJobRequest(Guid streamAlertId, StreamAlertFrequency frequency, DateTimeOffset startDate, DateTimeOffset? endDate)
    {
        StreamAlertId = streamAlertId;
        Frequency = frequency;
        StartDate = startDate;
        EndDate = endDate;
    }

    public Guid StreamAlertId { get; init; }
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public StreamAlertFrequency Frequency { get; init; }
    public DateTimeOffset StartDate { get; init; }
    public DateTimeOffset? EndDate { get; init; }
}
