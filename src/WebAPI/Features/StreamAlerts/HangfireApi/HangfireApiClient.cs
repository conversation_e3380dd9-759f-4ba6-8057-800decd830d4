using SharedKernel.Primitives.Abstract;
using WebAPI.Features.StreamAlerts.HangfireApi.GetPendingStreamAlertsJob;
using WebAPI.Features.StreamAlerts.HangfireApi.ScheduleStreamAlertJob;
using WebAPI.Features.StreamAlerts.HangfireApi.UpdateScheduleStreamAlertJob;
using WebAPI.Features.StreamAlerts.UpdateStreamAlert;

namespace WebAPI.Features.StreamAlerts.HangfireApi;

public interface IHangfireApi
{
    Task<Result<List<GetPendingStreamAlertsJobResponse>>> GetPendingStreamAlertsJob(CancellationToken cancellationToken);
    Task<Result<ScheduleStreamAlertJobResponse>> ScheduleStreamAlertJob(ScheduleStreamAlertJobRequest jobRequest, CancellationToken cancellationToken);
    Task<Result<UpdateScheduleStreamAlertJobResponse>> UpdateScheduleStreamAlertJob(string jobId, UpdateScheduleStreamAlertJobRequest jobRequest, CancellationToken cancellationToken);
    Task<Result> DeleteScheduleStreamAlertJob(string jobId, CancellationToken cancellationToken);
}

public sealed class HangfireApiClient : BaseApiClient, IHangfireApi
{
    public const string ClientName = "hanfigre-api";
    private readonly HttpClient _httpClient;
    private readonly ILogger<HangfireApiClient> _logger;

    public HangfireApiClient(IHttpClientFactory httpClientFactory, ILogger<HangfireApiClient> logger)
    {
        _httpClient = httpClientFactory.CreateClient(ClientName);
        _logger = logger;
    }

    public async Task<Result<ScheduleStreamAlertJobResponse>> ScheduleStreamAlertJob(ScheduleStreamAlertJobRequest jobRequest, CancellationToken cancellationToken)
    {
        var httpResponseMessage = await _httpClient.PostAsJsonAsync("streamalert/", jobRequest, cancellationToken);

        return await HandleResponse<ScheduleStreamAlertJobResponse>(httpResponseMessage, cancellationToken);
    }

    public async Task<Result<UpdateScheduleStreamAlertJobResponse>> UpdateScheduleStreamAlertJob(string jobId, UpdateScheduleStreamAlertJobRequest jobRequest, CancellationToken cancellationToken)
    {
        try
        {
            var httpResponseMessage = await _httpClient.PutAsJsonAsync($"streamalert/{jobId}", jobRequest, cancellationToken);

            if (httpResponseMessage.IsSuccessStatusCode)
            {
                // If successful, process the response as normal
                return await HandleResponse<UpdateScheduleStreamAlertJobResponse>(httpResponseMessage, cancellationToken);
            }

            if (httpResponseMessage.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                var errorContent = await httpResponseMessage.Content.ReadAsStringAsync(cancellationToken);

                if (!string.IsNullOrWhiteSpace(errorContent))
                {
                    // Attempt to deserialize the JSON error response
                    try
                    {
                        var errorResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<JobErrorResponse>(errorContent);

                        if (errorResponse != null)
                        {
                            // Construct an error message including the job ID if available
                            var jobIdInfo = errorResponse.JobId.Count != 0
                                ? $"Job ID: {string.Join(", ", errorResponse.JobId)}"
                                : "No Job ID provided";

                            return Result.Fail(new Error($"{string.Join(", ", errorResponse.Message)}. {jobIdInfo}")
                                .WithMetadata("StatusCode", 404));
                        }
                    }
                    catch (System.Text.Json.JsonException ex)
                    {
                        // Log or handle deserialization issue if needed
                        _logger.LogWarning(ex, "Failed to deserialize error response.");
                    }
                }

                return Result.Fail<UpdateScheduleStreamAlertJobResponse>("Job not found, but no additional details provided.");
            }

            // Handle other non-success status codes
            return Result.Fail<UpdateScheduleStreamAlertJobResponse>(
                $"Request failed with status code {(int)httpResponseMessage.StatusCode}: {httpResponseMessage.ReasonPhrase}");
        }
        catch (HttpRequestException ex)
        {
            // Handle HTTP-related exceptions
            return Result.Fail<UpdateScheduleStreamAlertJobResponse>($"HTTP Request Error: {ex.Message}");
        }
        catch (TaskCanceledException ex)
        {
            // Handle request cancellations (e.g., timeout)
            return Result.Fail<UpdateScheduleStreamAlertJobResponse>($"Request Cancelled: {ex.Message}");
        }
    }

    public async Task<Result> DeleteScheduleStreamAlertJob(string jobId, CancellationToken cancellationToken)
    {
        var httpResponseMessage = await _httpClient.DeleteAsync($"streamalert/{jobId}", cancellationToken);

        return httpResponseMessage.IsSuccessStatusCode ? Result.Ok() : Result.Fail(httpResponseMessage.ReasonPhrase);
    }

    public async Task<Result<List<GetPendingStreamAlertsJobResponse>>> GetPendingStreamAlertsJob(CancellationToken cancellationToken)
    {
        var httpResponseMessage = await _httpClient.GetAsync("streamalert/getpendingstreamalerts/", cancellationToken);

        return await HandleResponse<List<GetPendingStreamAlertsJobResponse>>(httpResponseMessage, cancellationToken);
    }
}
