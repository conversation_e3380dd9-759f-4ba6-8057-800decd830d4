using System.Text.Json.Serialization;
using SharedKernel.Entities;

namespace WebAPI.Features.StreamAlerts.HangfireApi.ScheduleStreamAlertJob;

public sealed record ScheduleStreamAlertJobRequest
{
    public ScheduleStreamAlertJobRequest(Guid streamAlertId, StreamAlertFrequency frequency, DateTime startDate, DateTime? endDate)
    {
        StreamAlertId = streamAlertId;
        Frequency = frequency;
        StartDate = startDate;
        EndDate = endDate;
    }

    public Guid StreamAlertId { get; init; }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public StreamAlertFrequency Frequency { get; init; }
    public DateTime StartDate { get; init; }
    public DateTime? EndDate { get; init; }
}
