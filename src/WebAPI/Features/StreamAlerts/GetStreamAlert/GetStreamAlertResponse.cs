using System.Text.Json.Serialization;
using SharedKernel.Primitives.Contracts;

namespace WebAPI.Features.StreamAlerts.GetStreamAlert;

public sealed class GetStreamAlertResponse(
    Guid id,
    StreamResponse stream,
    string name,
    string frequency,
    string recurrence,
    List<string> emails,
    List<UserDto> emailRecipients,
    DateTimeOffset startDate,
    DateTimeOffset? endDate,
    DistributionGroupResponse distributionGroup,
    string status,
    string hangfireId,
    DateTimeOffset? lastSent)
{
    public Guid Id { get; } = id;
    public StreamResponse Stream { get; } = stream;
    public string Frequency { get; } = frequency;
    public string Recurrence { get; } = recurrence;
    public DateTimeOffset StartDate { get; } = startDate;
    public DateTimeOffset? EndDate { get; } = endDate;
    public string HangfireId { get; } = hangfireId;
    public DateTimeOffset? LastSent { get; } = lastSent;
    public string Name { get; } = name;
    [JsonIgnore]
    public List<string> Emails { get; } = emails;
    public List<UserDto> EmailRecipients { get; private set; } = emailRecipients;
    public DistributionGroupResponse DistributionGroup { get; set; } = distributionGroup;
    public string Status { get; } = status;

    // Method to update the property value later
    public void UpdateEmailRecipients(List<UserDto> emailRecipients)
    {
        EmailRecipients = emailRecipients ?? new List<UserDto>();
    }
}

public class StreamResponse(Stream stream)
{
    public Guid Id { get; set; } = stream.Id.Value;
    public string Name { get; set; } = stream.Name;
    public string CompanyName { get; set; } = stream.CompanyName;
    public DateTimeOffset LastUpdatedUtc { get; set; } = stream.UpdatedDateUtc;
    public int? NumberOfAlerts { get; set; } = stream.NoOfArticles;
}
