using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.StreamAlerts.GetStreamAlert;

public sealed class GetStreamAlertEndpoint(ISender sender, IMapper mapper) : EndpointBaseAsync
    .WithRequest<GetStreamAlertRequest>
    .WithResult<ActionResult<GetStreamAlertResponse>>
{
    [HttpGet($"{ApiRoute.StreamAlertRoute}/{{id}}")]
    [SwaggerOperation(Tags = [nameof(StreamAlerts)])]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(GetStreamAlertResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(GetStreamAlertResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(GetStreamAlertResponse))]
    public override async Task<ActionResult<GetStreamAlertResponse>> HandleAsync(GetStreamAlertRequest request, CancellationToken cancellationToken = new())
    {
        var commandResult = await sender.Send(
            new GetStreamAlertCommand(request.StreamAlertId), cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Created($"{ApiRoute.StreamAlertRoute}/{commandResult.Value.Id}", mapper.Map<GetStreamAlertResponse>(commandResult.Value));
    }
}
