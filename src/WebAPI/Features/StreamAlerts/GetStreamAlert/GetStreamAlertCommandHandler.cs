using SharedKernel.Entities;
using SharedKernel.Primitives.Contracts;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services.NotificationApi;
using SharedKernel.Services.UserApi;
using WebAPI.Persistence;

namespace WebAPI.Features.StreamAlerts.GetStreamAlert;

internal sealed class GetStreamAlertCommandHandler(
    ApplicationDbContext dbContext,
    IUserApiClient userClient,
    INotificationApiClient notificationApiClient,
    ILogger<GetStreamAlertCommandHandler> logger)
    : ICommandHandler<GetStreamAlertCommand, GetStreamAlertResponse>
{
    public async Task<Result<GetStreamAlertResponse>> Handle(GetStreamAlertCommand command, CancellationToken cancellationToken)
    {
        var streamAlert = await dbContext.StreamAlerts
            .Where(sa => sa.Id == new StreamAlertId(command.StreamAlertId))
            .Select(sa => new GetStreamAlertResponse(
                sa.Id.Value,
                new StreamResponse(sa.Stream),
                sa.Name,
                sa.Frequency.ToString(),
                sa.Recurrence.ToString(),
                SplitEmails(sa.EmailRecipients),
                new List<UserDto>(),
                sa.StartDateTime.DateTime,
                sa.EndDateTime.HasValue ? sa.EndDateTime.Value.DateTime : null,
                sa.DistributionGroupId.HasValue
                    ? new DistributionGroupResponse(sa.DistributionGroupId.Value) // Only pass a value if it exists
                    : null, // Handle null DistributionGroupId gracefully
                sa.Status.ToString(),
                sa.HangfireId.ToString(),
                sa.LastSent))
            .FirstOrDefaultAsync(cancellationToken);

        if (streamAlert == null)
        {
            // Log a warning when the stream alert is not found
            logger.LogWarning("No StreamAlert found for StreamId: {StreamId}", command.StreamAlertId);

            // Return a Result indicating failure with an appropriate message
            return Result.Fail($"StreamAlert with ID {command.StreamAlertId} not found.");
        }

        if (streamAlert.Emails.Count > 0)
        {
            var users = await userClient.GetUserByEmail(streamAlert.Emails, cancellationToken);
            if (users is { Value: not null })
            {
                streamAlert.UpdateEmailRecipients(users.Value);
            }
        }

        if (streamAlert.DistributionGroup != null)
        {
            // Fetch the distribution group name from the notification API client
            var distributionGroupResult = await notificationApiClient.GetDistributionGroupsAsync(
                [streamAlert.DistributionGroup.Id], cancellationToken);
            var distributionGroupDto = distributionGroupResult.Value.FirstOrDefault();
            if (distributionGroupDto != null)
            {
                streamAlert.DistributionGroup = new DistributionGroupResponse(distributionGroupDto.Id)
                {
                    Id = distributionGroupDto.Id,
                    Name = distributionGroupDto.Name,
                    Description = distributionGroupDto.Description,
                    TotalUsers = distributionGroupDto.TotalUsers
                };
            }
        }

        return streamAlert;
    }

    private static List<string> SplitEmails(string commaSeparatedEmails)
    {
        if (string.IsNullOrWhiteSpace(commaSeparatedEmails))
        {
            return new List<string>();
        }

        return commaSeparatedEmails
            .Split(',', StringSplitOptions.RemoveEmptyEntries) // Split on commas and skip empty entries
            .Select(email => email.Trim()) // Trim whitespace around each email
            .Where(email => !string.IsNullOrEmpty(email)) // Remove any remaining empty strings
            .ToList(); // Convert the result to a List<string>
    }
}
