using SharedKernel.Entities;

namespace WebAPI.Features.StreamAlerts.CreateStreamAlert;

public sealed record CreateStreamAlertRequest(
    Guid StreamId,
    StreamAlertFrequency? Frequency,
    StreamAlertRecurrence Recurrence,
    DateTime StartDateTime,
    DateTime? EndDateTime,
    Guid? TemplateId,
    string Name,
    List<string> RecipientEmails,
    Guid? DistributionGroupId);

internal sealed class CreateStreamAlertRequestValidator : AbstractValidator<CreateStreamAlertRequest>
{
    public CreateStreamAlertRequestValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.StreamId).NotEmpty().WithMessage("StreamId is required.");
        RuleFor(x => x.Frequency)
            .Must(frequency => !frequency.HasValue || Enum.IsDefined(frequency.Value))
            .WithMessage("Frequency must either be null or a valid StreamAlertFrequency value.");
        RuleFor(x => x.Recurrence).IsInEnum().WithMessage("Recurrence must be a valid value.");
        RuleFor(x => x.StartDateTime)
            .NotEmpty().WithMessage("StartDateTime is required.")
            .GreaterThanOrEqualTo(DateTime.Now).WithMessage("StartDateTime cannot be in the past.");

        RuleFor(x => x)
            .Must(x => x.DistributionGroupId.HasValue || x.RecipientEmails is { Count: > 0 })
            .WithMessage("Either DistributionGroupId must be provided or at least one RecipientEmail is required.");

        RuleFor(x => x.EndDateTime)
            .GreaterThan(x => x.StartDateTime).When(x => x.EndDateTime.HasValue)
            .WithMessage("EndDateTime must be later than StartDateTime.");
    }
}
