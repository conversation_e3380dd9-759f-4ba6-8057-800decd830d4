using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;

namespace WebAPI.Features.StreamAlerts.CreateStreamAlert;

public sealed record CreateStreamAlertCommand(
    StreamId StreamId,
    StreamAlertRecurrence Recurrence,
    DateTime StartDateTime,
    DateTime? EndDateTime,
    Guid? TemplateId,
    string Name,
    List<string> RecipientEmails,
    Guid? DistributionGroupId,
    StreamAlertFrequency? Frequency = StreamAlertFrequency.None // Default value of None
) : ICommand<StreamAlert>;

internal sealed class CreateStreamAlertCommandValidator : AbstractValidator<CreateStreamAlertCommand>
{
    public CreateStreamAlertCommandValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.StreamId.Value).NotEmpty();
        RuleFor(x => x.Frequency)
            .Must(frequency => !frequency.HasValue || Enum.IsDefined(frequency.Value))
            .WithMessage("Frequency must either be null or a valid StreamAlertFrequency value.");
        RuleFor(x => x.StartDateTime)
            .NotEmpty()
            .GreaterThanOrEqualTo(DateTime.Now);
        RuleFor(x => x.EndDateTime)
            .GreaterThan(x => x.StartDateTime)
            .When(x => x.EndDateTime.HasValue);
        RuleFor(x => x)
            .Must(x => x.DistributionGroupId.HasValue || x.RecipientEmails is { Count: > 0 })
            .WithMessage("Either DistributionGroupId must be provided or at least one RecipientEmail is required.");
    }
}
