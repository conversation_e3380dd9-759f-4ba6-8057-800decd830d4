using SharedKernel.Entities;
using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.StreamAlerts.CreateStreamAlert;

public class CreateStreamAlertEndpoint : EndpointBaseAsync
    .WithRequest<CreateStreamAlertRequest>
    .WithResult<ActionResult<CreateStreamAlertResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public CreateStreamAlertEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpPost(ApiRoute.StreamAlertRoute)]
    [SwaggerOperation(Tags = [nameof(StreamAlerts)])]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(CreateStreamAlertResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<CreateStreamAlertResponse>> HandleAsync(CreateStreamAlertRequest request, CancellationToken cancellationToken = new())
    {
        var commandResult = await _sender.Send(
            new CreateStreamAlertCommand(
                new StreamId(request.StreamId),
                request.Recurrence,
                request.StartDateTime,
                request.EndDateTime,
                request.TemplateId,
                request.Name,
                request.RecipientEmails,
                request.DistributionGroupId, request.Frequency),
            cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Created($"{ApiRoute.StreamAlertRoute}/{commandResult.Value.Id}", _mapper.Map<CreateStreamAlertResponse>(commandResult.Value));
    }
}
