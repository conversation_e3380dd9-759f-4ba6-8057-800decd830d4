using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Features.StreamAlerts.HangfireApi;
using WebAPI.Features.StreamAlerts.HangfireApi.ScheduleStreamAlertJob;
using WebAPI.Features.StreamAlerts.UpdateStreamAlertLastSent;
using WebAPI.Persistence;

namespace WebAPI.Features.StreamAlerts.CreateStreamAlert;

internal sealed class CreateStreamAlertCommandHandler : ICommandHandler<CreateStreamAlertCommand, StreamAlert>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IHangfireApi _hangfireApi;
    private readonly ILogger<CreateStreamAlertCommandHandler> _logger;
    private readonly IAlertCalculator _alertCalculator;

    public CreateStreamAlertCommandHandler(
        ApplicationDbContext dbContext,
        IHangfireApi hangfireApi,
        ILogger<CreateStreamAlertCommandHandler> logger, IAlertCalculator alertCalculator)
    {
        _dbContext = dbContext;
        _hangfireApi = hangfireApi;
        _logger = logger;
        _alertCalculator = alertCalculator;
    }

    public async Task<Result<StreamAlert>> Handle(CreateStreamAlertCommand request, CancellationToken cancellationToken)
    {
        var stream = await _dbContext.Streams.FindAsync([request.StreamId], cancellationToken);

        if (stream is null)
        {
            return Result.Fail(new Error($"The stream with id {request.StreamId.Value} was not found",
                new Error("StreamId")));
        }

        // Set Frequency to StreamAlertFrequency.None if not provided
        request = request with
        {
            Frequency = request.Frequency ?? StreamAlertFrequency.None
        };

        var streamAlert = StreamAlert.Create(
            request.StreamId,
            request.Frequency.Value,
            request.Recurrence,
            request.StartDateTime,
            request.EndDateTime,
            request.TemplateId,
            request.Name,
            request.RecipientEmails,
            request.DistributionGroupId);

        var hangfireApiResult = await _hangfireApi.ScheduleStreamAlertJob(
            new ScheduleStreamAlertJobRequest(
                streamAlert.Id.Value,
                request.Frequency.Value,
                request.StartDateTime,
                request.EndDateTime), cancellationToken);

        if (hangfireApiResult.IsFailed)
        {
            _logger.LogError("Failed to access ScheduleStreamAlert from the HangfireApi API service");

            return Result.Fail(hangfireApiResult.Errors);
        }

        streamAlert.SetHangfireId(hangfireApiResult.Value.JobId);

        var nextAlertDate = _alertCalculator.CalculateNextAlertDate(streamAlert.StartDateTime, streamAlert.EndDateTime,
            streamAlert.Frequency);

        streamAlert.UpdateNextAlertDate(nextAlertDate);

        await _dbContext.StreamAlerts.AddAsync(streamAlert, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return streamAlert;
    }
}
