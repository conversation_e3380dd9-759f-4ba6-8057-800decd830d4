using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.StreamAlerts.GetPendingStreamAlerts;

public class GetPendingStreamAlertsEndpoint : EndpointBaseAsync
    .WithoutRequest
    .WithResult<ActionResult<IEnumerable<GetPendingStreamAlertsResponse>>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetPendingStreamAlertsEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpGet($"{ApiRoute.StreamAlertRoute}/upcoming")]
    [SwaggerOperation(Tags = [nameof(StreamAlerts)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IEnumerable<GetPendingStreamAlertsResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<IEnumerable<GetPendingStreamAlertsResponse>>> HandleAsync(CancellationToken cancellationToken = new())
    {
        var queryResult = await _sender.Send(new GetPendingStreamAlertsQuery(), cancellationToken);

        if (queryResult.IsFailed)
        {
            return BadRequest(queryResult.Errors.ToProblemDetails());
        }

        return Ok(_mapper.Map<IEnumerable<GetPendingStreamAlertsResponse>>(queryResult.Value));
    }
}
