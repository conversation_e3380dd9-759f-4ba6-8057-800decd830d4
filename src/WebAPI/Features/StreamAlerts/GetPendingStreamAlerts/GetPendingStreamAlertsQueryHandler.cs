using SharedKernel.Primitives.Interfaces;
using WebAPI.Features.StreamAlerts.HangfireApi;

namespace WebAPI.Features.StreamAlerts.GetPendingStreamAlerts;

public sealed record PendingStreamAlert(
    string StreamAlertId,
    string JobId,
    DateTimeOffset NextExecution,
    DateTimeOffset LastExecution);

internal sealed class GetPendingStreamAlertsQueryHandler : IQueryHandler<GetPendingStreamAlertsQuery, IEnumerable<PendingStreamAlert>>
{
    private readonly IHangfireApi _hangfireApi;
    private readonly ILogger<GetPendingStreamAlertsQueryHandler> _logger;

    public GetPendingStreamAlertsQueryHandler(
        IHangfireApi hangfireApi,
        ILogger<GetPendingStreamAlertsQueryHandler> logger)
    {
        _hangfireApi = hangfireApi;
        _logger = logger;
    }

    public async Task<Result<IEnumerable<PendingStreamAlert>>> Handle(GetPendingStreamAlertsQuery request, CancellationToken cancellationToken)
    {
        var hangfireApiResult = await _hangfireApi.GetPendingStreamAlertsJob(cancellationToken);

        if (hangfireApiResult.IsFailed)
        {
            _logger.LogError("Failed to access ScheduleStreamAlert from the HangfireApi API service");

            return Result.Fail(hangfireApiResult.Errors);
        }

        var nextExecutionDeadline = DateTimeOffset.UtcNow.AddHours(24);

        return hangfireApiResult.Value
            .Where(i => i.NextExecution < nextExecutionDeadline)
            .Select(i =>
                new PendingStreamAlert(
                    i.StreamAlertId,
                    i.JobId,
                    i.NextExecution,
                    i.LastExecution))
            .ToList();
    }
}
