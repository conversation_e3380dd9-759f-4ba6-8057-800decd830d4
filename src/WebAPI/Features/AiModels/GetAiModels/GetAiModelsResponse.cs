using SharedKernel.Entities;
using WebAPI.Infrastructure.Mappers;

namespace WebAPI.Features.AiModels.GetAiModels;

public record GetAiModelsResponse : IMapFrom<AiModel>
{
    public Guid Id { get; init; }
    public string Name { get; init; }

    public void Mapping(Profile profile)
    {
        profile.CreateMap<AiModel, GetAiModelsResponse>()
            .ForMember(destination => destination.Id,
                source => source.MapFrom(s => s.Id.Value))
            ;
    }
}
