using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.AiModels.GetAiModels;

public class GetAiModelsEndpoint : EndpointBaseAsync
    .WithoutRequest
    .WithResult<ActionResult<GetAiModelsResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GetAiModelsEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpGet(ApiRoute.AiModelRoute)]
    [SwaggerOperation(Tags = [nameof(AiModels)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IEnumerable<GetAiModelsResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<GetAiModelsResponse>> HandleAsync(CancellationToken cancellationToken = new())
    {
        var queryResult = await _sender.Send(new GetAiModelsQuery(), cancellationToken);

        return Ok(_mapper.Map<IEnumerable<GetAiModelsResponse>>(queryResult.Value));
    }
}

