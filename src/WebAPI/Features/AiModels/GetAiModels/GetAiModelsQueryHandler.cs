using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Persistence;

namespace WebAPI.Features.AiModels.GetAiModels;

internal sealed class GetAiModelsQueryHandler : IQueryHandler<GetAiModelsQuery, PaginatedQueryResult<AiModel>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetAiModelsQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<PaginatedQueryResult<AiModel>>> Handle(GetAiModelsQuery request, CancellationToken cancellationToken)
    {
        IQueryable<AiModel> query = _dbContext.AiModels.OrderBy(x => x.Id);

        return await PaginatedQueryResult<AiModel>.CreateAsync(query, 1, Int32.MaxValue, cancellationToken);
    }
}
