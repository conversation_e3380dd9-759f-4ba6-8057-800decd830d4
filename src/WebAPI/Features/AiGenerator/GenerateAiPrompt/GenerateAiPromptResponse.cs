using SharedKernel.Services.ColibriApi.ExpandClientProfile;
using WebAPI.Infrastructure.Mappers;

namespace WebAPI.Features.AiGenerator.GenerateAiPrompt;

public sealed record GenerateAiPromptResponse : IMapFrom<ExpandClientProfileResponse>
{
    public string GeneratedPrompt { get; init; }
    public int PromptId { get; init; }

    public void Mapping(Profile profile)
    {
        profile.CreateMap<ExpandClientProfileResponse, GenerateAiPromptResponse>()
            // Map `GeneratedPrompt` property from `Response` property
            .ForMember(destination => destination.GeneratedPrompt,
                source => source.MapFrom(s => s.Response))
            // Map `PromptId` property from `PromptId`
            .ForMember(destination => destination.PromptId,
                source => source.MapFrom(s => s.PromptId));
    }
}
