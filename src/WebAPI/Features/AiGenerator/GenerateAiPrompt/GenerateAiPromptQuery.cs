using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services.ColibriApi.ExpandClientProfile;

namespace WebAPI.Features.AiGenerator.GenerateAiPrompt;

public sealed record GenerateAiPromptQuery(string BaseDescription) : IQuery<ExpandClientProfileResponse>;

internal sealed class GenerateAiPromptQueryValidator : AbstractValidator<GenerateAiPromptQuery>
{
    public GenerateAiPromptQueryValidator()
    {
        RuleFor(x => x.BaseDescription).NotEmpty();
    }
}
