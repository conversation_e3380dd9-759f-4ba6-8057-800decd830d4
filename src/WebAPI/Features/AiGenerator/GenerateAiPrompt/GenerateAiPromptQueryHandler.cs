using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services.ColibriApi;
using SharedKernel.Services.ColibriApi.ExpandClientProfile;
using WebAPI.Infrastructure.Static;

namespace WebAPI.Features.AiGenerator.GenerateAiPrompt;

internal sealed class GenerateAiPromptQueryHandler : IQueryHandler<GenerateAiPromptQuery, ExpandClientProfileResponse>
{
    private readonly IColibriApi _colibriApi;
    private readonly ILogger<GenerateAiPromptQueryHandler> _logger;

    public GenerateAiPromptQueryHandler(IColibriApi colibriApi,
        ILogger<GenerateAiPromptQueryHandler> logger,
        IHostEnvironment hostEnvironment)
    {
        _colibriApi = colibriApi;
        _logger = logger;
    }

    public async Task<Result<ExpandClientProfileResponse>> Handle(GenerateAiPromptQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Using {ModelName} model", DefaultDehavillandConfigurations.ModelName);

        var colibriApiResult = await _colibriApi.ExpandClientProfileAsync(
            new ExpandClientProfileRequest(
                clientId: DefaultDehavillandConfigurations.DehavillandCompanyId,
                clientName: DefaultDehavillandConfigurations.DehavillandCompanyName,
                clientProfile: request.BaseDescription,
                modelName: DefaultDehavillandConfigurations.ModelName),
            cancellationToken);

        if (colibriApiResult.IsFailed)
        {
            _logger.LogError(
                "Failed to access ExpandClientProfile from the Colibri API service for Company ID: {CompanyId}, Company Name: {CompanyName}",
                DefaultDehavillandConfigurations.DehavillandCompanyId,
                DefaultDehavillandConfigurations.DehavillandCompanyName);

            return Result.Fail(colibriApiResult.Errors);
        }

        return colibriApiResult.Value;
    }
}
