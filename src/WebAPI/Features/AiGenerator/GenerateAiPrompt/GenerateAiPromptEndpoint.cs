using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Extensions;

namespace WebAPI.Features.AiGenerator.GenerateAiPrompt;

public class GenerateAiPromptEndpoint : EndpointBaseAsync
    .WithRequest<GenerateAiPromptRequest>
    .WithResult<ActionResult<GenerateAiPromptResponse>>
{
    private readonly ISender _sender;
    private readonly IMapper _mapper;

    public GenerateAiPromptEndpoint(ISender sender, IMapper mapper)
    {
        _sender = sender;
        _mapper = mapper;
    }

    [HttpPost($"{ApiRoute.AiGeneratorRoute}/generate-prompt")]
    [SwaggerOperation(Tags = [nameof(AiGenerator)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(GenerateAiPromptResponse))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<GenerateAiPromptResponse>> HandleAsync(GenerateAiPromptRequest request, CancellationToken cancellationToken = new())
    {
        var commandResult = await _sender.Send(
            new GenerateAiPromptQuery(request.BasePrompt), cancellationToken);

        if (commandResult.IsFailed)
        {
            return BadRequest(commandResult.Errors.ToProblemDetails());
        }

        return Ok(_mapper.Map<GenerateAiPromptResponse>(commandResult.Value));
    }
}
