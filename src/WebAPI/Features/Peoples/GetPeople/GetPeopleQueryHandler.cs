using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Persistence;

namespace WebAPI.Features.Peoples.GetPeople;

internal sealed class GetPeopleQueryHandler : IQueryHandler<GetPeopleQuery, PaginatedResponse<GetPeopleResponse>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetPeopleQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<PaginatedResponse<GetPeopleResponse>>> Handle(GetPeopleQuery request, CancellationToken cancellationToken)
    {
        IQueryable<PeopleTag> query = _dbContext.PeopleTags.Select(x => x);

        if (!string.IsNullOrWhiteSpace(request.SearchKeyword))
        {
            query = query.Where(p => EF.Functions.Like(p.Name, $"%{request.SearchKeyword}%"));
        }

        if (request.SortProperty.HasValue)
        {
            query = ApplySorting(query, request.SortProperty, request.SortType);
        }

        var result = await PaginatedQueryResult<PeopleTag>.CreateAsync(query, request.PageNumber, request.PageSize,
            cancellationToken);

        var people = new List<GetPeopleResponse>();

        foreach (var item in result)
        {
            var person = new GetPeopleResponse(item.Id, item.Name);

            people.Add(person);
        }

        return new PaginatedResponse<GetPeopleResponse>(people, result.PageNumber,
            result.PageSize, result.TotalRecords);
    }

    private static IQueryable<PeopleTag> ApplySorting(
        IQueryable<PeopleTag> query,
        PeopleSortProperty? sortType,
        PropertySortType? propertySortOrder)
    {
        return sortType switch
        {
            _ => query.OrderBySortOrder(u => u, propertySortOrder)
        };
    }
}
