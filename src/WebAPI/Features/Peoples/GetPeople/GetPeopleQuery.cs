using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;

namespace WebAPI.Features.Peoples.GetPeople;

public enum PeopleSortProperty
{
    Name = 1
}

public sealed record GetPeopleQuery(
    string SearchKeyword,
    int PageNumber,
    int PageSize,
    PeopleSortProperty? SortProperty = null,
    PropertySortType? SortType = null) : IQuery<PaginatedResponse<GetPeopleResponse>>;
