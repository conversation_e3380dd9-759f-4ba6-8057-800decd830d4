using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.Peoples.GetPeople;

public class GetPeopleEndpoint : EndpointBaseAsync
    .WithRequest<GetPeopleFilterRequest>
    .WithResult<ActionResult<PaginatedResponse<GetPeopleResponse>>>
{
    private readonly ISender _sender;

    public GetPeopleEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpGet(ApiRoute.PeopleRoute)]
    [SwaggerOperation(Tags = [nameof(Peoples)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PaginatedResponse<GetPeopleResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<PaginatedResponse<GetPeopleResponse>>> HandleAsync([FromQuery] GetPeopleFilterRequest request, CancellationToken cancellationToken = new())
    {
        var queryResult = await _sender.Send(new GetPeopleQuery(
                request.SearchKeyword,
                request.PageNumber,
                request.PageSize,
                SortProperty: request.SortProperty,
                SortType: request.SortType), cancellationToken);

        return Ok(queryResult.Value);
    }
}

