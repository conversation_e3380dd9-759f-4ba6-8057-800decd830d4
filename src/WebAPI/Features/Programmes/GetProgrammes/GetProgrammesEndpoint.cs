using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.Programmes.GetProgrammes;

public class GetProgrammesEndpoint : EndpointBaseAsync
    .WithRequest<GetProgrammesFilterRequest>
    .WithResult<ActionResult<PaginatedResponse<GetProgrammesResponse>>>
{
    private readonly ISender _sender;

    public GetProgrammesEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpGet(ApiRoute.ProgrammeRoute)]
    [SwaggerOperation(Tags = [nameof(Programmes)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PaginatedResponse<GetProgrammesResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<PaginatedResponse<GetProgrammesResponse>>> HandleAsync(
        [FromQuery] GetProgrammesFilterRequest request,
        CancellationToken cancellationToken = new())
    {
        var queryResult = await _sender.Send(new GetProgrammesQuery(
                request.SearchKeyword,
                request.PageNumber,
                request.PageSize,
                SortProperty: request.SortProperty,
                SortType: request.SortType), cancellationToken);

        return Ok(queryResult.Value);
    }
}

