using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Persistence;

namespace WebAPI.Features.Programmes.GetProgrammes;

internal sealed class GetProgrammesQueryHandler : IQueryHandler<GetProgrammesQuery, PaginatedResponse<GetProgrammesResponse>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetProgrammesQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<PaginatedResponse<GetProgrammesResponse>>> Handle(GetProgrammesQuery request, CancellationToken cancellationToken)
    {
        IQueryable<ProgrammeTag> query = _dbContext.ProgrammeTags.Select(p => p);

        if (!string.IsNullOrWhiteSpace(request.SearchKeyword))
        {
            query = query.Where(p => EF.Functions.Like(p.Name, $"%{request.SearchKeyword}%"));
        }

        if (request.SortProperty.HasValue)
        {
            query = ApplySorting(query, request.SortProperty, request.SortType);
        }

        var result = await PaginatedQueryResult<ProgrammeTag>.CreateAsync(query, request.PageNumber, request.PageSize,
            cancellationToken);

        var people = new List<GetProgrammesResponse>();

        foreach (var item in result)
        {
            var person = new GetProgrammesResponse(item.Id, item.Name);

            people.Add(person);
        }

        return new PaginatedResponse<GetProgrammesResponse>(people, result.PageNumber,
            result.PageSize, result.TotalRecords);
    }


    private static IQueryable<ProgrammeTag> ApplySorting(
        IQueryable<ProgrammeTag> query,
        ProgrammeSortProperty? sortType,
        PropertySortType? propertySortOrder)
    {
        return sortType switch
        {
            _ => query.OrderBySortOrder(u => u, propertySortOrder)
        };
    }
}
