using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;

namespace WebAPI.Features.Programmes.GetProgrammes;

public enum ProgrammeSortProperty
{
    Name = 1
}

public sealed record GetProgrammesQuery(
    string SearchKeyword,
    int PageNumber,
    int PageSize,
    ProgrammeSortProperty? SortProperty = null,
    PropertySortType? SortType = null) : IQuery<PaginatedResponse<GetProgrammesResponse>>;
