using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;

namespace WebAPI.Features.Organisations.GetOrganisations;

public enum OrganisationSortProperty
{
    Name = 1
}

public sealed record GetOrganisationsQuery(
    string SearchKeyword,
    int PageNumber,
    int PageSize,
    OrganisationSortProperty? SortProperty = null,
    PropertySortType? SortType = null) : IQuery<PaginatedResponse<GetOrganisationResponse>>;
