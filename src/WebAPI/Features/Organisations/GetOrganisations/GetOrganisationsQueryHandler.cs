using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Persistence;

namespace WebAPI.Features.Organisations.GetOrganisations;

internal sealed class
    GetOrganisationsQueryHandler : IQueryHandler<GetOrganisationsQuery, PaginatedResponse<GetOrganisationResponse>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetOrganisationsQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<PaginatedResponse<GetOrganisationResponse>>> Handle(GetOrganisationsQuery request,
        CancellationToken cancellationToken)
    {
        IQueryable<OrganisationTag> query = _dbContext.OrganisationTags.Select(o => o);

        if (!string.IsNullOrWhiteSpace(request.SearchKeyword))
        {
            query = query.Where(p => EF.Functions.Like(p.Name, $"%{request.SearchKeyword}%"));
        }

        if (request.SortProperty.HasValue)
        {
            query = ApplySorting(query, request.SortProperty, request.SortType);
        }

        var result = await PaginatedQueryResult<OrganisationTag>.CreateAsync(query, request.PageNumber, request.PageSize,
            cancellationToken);

        var organisations = new List<GetOrganisationResponse>();

        foreach (var item in result)
        {
            var organisation = new GetOrganisationResponse(item.Id, item.Name);

            organisations.Add(organisation);
        }

        return new PaginatedResponse<GetOrganisationResponse>(organisations, result.PageNumber,
            result.PageSize, result.TotalRecords);
    }

    private static IQueryable<OrganisationTag> ApplySorting(
        IQueryable<OrganisationTag> query,
        OrganisationSortProperty? sortType,
        PropertySortType? propertySortOrder)
    {
        return sortType switch
        {
            _ => query.OrderBySortOrder(u => u, propertySortOrder)
        };
    }
}
