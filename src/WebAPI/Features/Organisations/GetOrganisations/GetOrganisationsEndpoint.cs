using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.Organisations.GetOrganisations;

public class GetOrganisationsEndpoint : EndpointBaseAsync
    .WithRequest<GetOrganisationsFilterRequest>
    .WithResult<ActionResult<PaginatedResponse<GetOrganisationResponse>>>
{
    private readonly ISender _sender;

    public GetOrganisationsEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpGet(ApiRoute.OrganisationRoute)]
    [SwaggerOperation(Tags = [nameof(Organisations)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PaginatedResponse<GetOrganisationResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<PaginatedResponse<GetOrganisationResponse>>> HandleAsync(
        [FromQuery] GetOrganisationsFilterRequest request,
        CancellationToken cancellationToken = new())
    {
        var queryResult = await _sender.Send(new GetOrganisationsQuery(
                request.SearchKeyword,
                request.PageNumber,
                request.PageSize,
                SortProperty: request.SortProperty,
                SortType: request.SortType), cancellationToken);

        return Ok(queryResult.Value);
    }
}

