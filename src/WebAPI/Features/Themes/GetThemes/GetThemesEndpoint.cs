using WebAPI.Infrastructure;
using WebAPI.Infrastructure.Contracts;

namespace WebAPI.Features.Themes.GetThemes;

public class GetThemesEndpoint : EndpointBaseAsync
    .WithRequest<GetThemesFilterRequest>
    .WithResult<ActionResult<PaginatedResponse<GetThemesResponse>>>
{
    private readonly ISender _sender;

    public GetThemesEndpoint(ISender sender)
    {
        _sender = sender;
    }

    [HttpGet(ApiRoute.ThemeRoute)]
    [SwaggerOperation(Tags = [nameof(Themes)])]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PaginatedResponse<GetThemesResponse>))]
    [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetailsResponse))]
    public override async Task<ActionResult<PaginatedResponse<GetThemesResponse>>> HandleAsync([FromQuery] GetThemesFilterRequest request, CancellationToken cancellationToken = new())
    {
        var queryResult = await _sender.Send(new GetThemesQuery(
                request.SearchKeyword,
                request.PageNumber,
                request.PageSize,
                SortProperty: request.SortProperty,
                SortType: request.SortType), cancellationToken);

        return Ok(queryResult.Value);
    }
}

