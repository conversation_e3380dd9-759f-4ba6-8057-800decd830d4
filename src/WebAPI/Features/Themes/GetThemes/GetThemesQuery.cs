using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;

namespace WebAPI.Features.Themes.GetThemes;

public enum ThemesSortProperty
{
    Name = 1
}

public sealed record GetThemesQuery(
    string SearchKeyword,
    int PageNumber,
    int PageSize,
    ThemesSortProperty? SortProperty = null,
    PropertySortType? SortType = null) : IQuery<PaginatedResponse<GetThemesResponse>>;
