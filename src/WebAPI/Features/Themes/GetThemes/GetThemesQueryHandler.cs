using SharedKernel.Entities;
using SharedKernel.Primitives.Interfaces;
using WebAPI.Infrastructure.Contracts;
using WebAPI.Infrastructure.Enums;
using WebAPI.Infrastructure.Extensions;
using WebAPI.Persistence;

namespace WebAPI.Features.Themes.GetThemes;

internal sealed class GetThemesQueryHandler : IQueryHandler<GetThemesQuery, PaginatedResponse<GetThemesResponse>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetThemesQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<PaginatedResponse<GetThemesResponse>>> Handle(GetThemesQuery request, CancellationToken cancellationToken)
    {
        IQueryable<ThemeTag> query = _dbContext.ThemeTags.Select(x => x);

        if (!string.IsNullOrWhiteSpace(request.SearchKeyword))
        {
            query = query.Where(p => EF.Functions.Like(p.Name, $"%{request.SearchKeyword}%"));
        }

        if (request.SortProperty.HasValue)
        {
            query = ApplySorting(query, request.SortProperty, request.SortType);
        }

        var result = await PaginatedQueryResult<ThemeTag>.CreateAsync(query, request.PageNumber, request.PageSize,
            cancellationToken);

        var themesResponses = new List<GetThemesResponse>();

        foreach (var item in result)
        {
            var themesResponse = new GetThemesResponse(item.Id, item.Name);

            themesResponses.Add(themesResponse);
        }

        return new PaginatedResponse<GetThemesResponse>(themesResponses, result.PageNumber,
            result.PageSize, result.TotalRecords);
    }

    private static IQueryable<ThemeTag> ApplySorting(
        IQueryable<ThemeTag> query,
        ThemesSortProperty? sortType,
        PropertySortType? propertySortOrder)
    {
        return sortType switch
        {
            _ => query.OrderBySortOrder(u => u, propertySortOrder)
        };
    }
}
