using Microsoft.EntityFrameworkCore.ChangeTracking;
using SharedKernel.DataAudit;
using SharedKernel.Primitives.CustomAttributes;
using SharedKernel.Primitives.Interfaces;
using SharedKernel.Services;

namespace WebAPI.Persistence;

internal interface IAuditableContext
{
    DbSet<DataAuditTransaction> DataAuditTransactions { get; set; }
}

public abstract class AuditableContext : DbContext, IAuditableContext
{
    private readonly ICurrentUserService _currentUserService;
    private readonly IDateTimeService _dateTimeService;

    protected AuditableContext(
        DbContextOptions<ApplicationDbContext> options,
        IDateTimeService dateTimeService,
        ICurrentUserService currentUserService) : base(options)
    {
        _dateTimeService = dateTimeService ?? throw new ArgumentNullException(nameof(dateTimeService));

        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    }

    public DbSet<DataAuditTransaction> DataAuditTransactions { get; set; }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = new())
    {
        var auditableEntries = GetAuditableEntries();

        var addedEntities = auditableEntries[EntityState.Added].ToList();
        var deletedEntries = auditableEntries[EntityState.Deleted]
            .Select(p => GetEntityAuditDetails(p, EntityState.Deleted))
            .SelectMany(result => result.ToList())
            .ToList();

        UpdateAuditableRecords();
        UpdateSoftDeleteRecords();

        var modifiedEntities = auditableEntries[EntityState.Modified]
            .Select(p => GetEntityAuditDetails(p, EntityState.Modified))
            .SelectMany(result => result.ToList())
            .ToList();

        SaveAuditableRecords(addedEntities, deletedEntries, modifiedEntities);

        return base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateSoftDeleteRecords()
    {
        var entries = ChangeTracker.Entries<ISoftDeletable>()
            .Where(p => p.State == EntityState.Deleted);

        foreach (EntityEntry<ISoftDeletable> softDeletable in entries)
        {
            softDeletable.State = EntityState.Modified;
            softDeletable.Entity.IsDeleted = true;
            softDeletable.Entity.DeletedOnUtc = DateTimeOffset.UtcNow;
        }
    }

    private void UpdateAuditableRecords()
    {
        var auditableEntries = ChangeTracker
            .Entries<AuditableEntity>().Where(p => p.State is EntityState.Added or EntityState.Modified);

        foreach (var entry in auditableEntries)
        {
            switch (entry.State)
            {
                case EntityState.Modified:
                    entry.Entity.UpdatedDateUtc = _dateTimeService.UtcNow;
                    entry.Entity.UpdatedBy = _currentUserService.Id;
                    break;
                case EntityState.Added:
                    entry.Entity.CreatedDateUtc = _dateTimeService.UtcNow;
                    entry.Entity.CreatedBy = _currentUserService.Id;
                    entry.Entity.UpdatedDateUtc = _dateTimeService.UtcNow;
                    entry.Entity.UpdatedBy = _currentUserService.Id;
                    break;
            }
        }
    }

    private void SaveAuditableRecords(List<EntityEntry> addedEntities, List<DataAuditTransactionDetail> deletedEntries, List<DataAuditTransactionDetail> modifiedEntities)
    {
        var auditDetails = new List<DataAuditTransactionDetail>();
        auditDetails.AddRange(addedEntities.Select(p => GetEntityAuditDetails(p, EntityState.Added)).SelectMany(result => result));
        auditDetails.AddRange(deletedEntries);
        auditDetails.AddRange(modifiedEntities);

        if (auditDetails.Count == 0)
        {
            return;
        }

        DataAuditTransactions.Add(new DataAuditTransaction
        {
            EventDateUtc = _dateTimeService.UtcNow,
            IdentityUserId = _currentUserService.IdentityId,
            TransactionDetails = auditDetails
        });

        base.SaveChanges();
    }

    private ILookup<EntityState, EntityEntry> GetAuditableEntries()
    {
        IEnumerable<EntityEntry> auditableEntries;

        try
        {
            Func<EntityEntry, bool> canBeAudited = p => Attribute.GetCustomAttribute(p.Entity.GetType(), typeof(NonAuditableAttribute)) == null;

            auditableEntries = ChangeTracker.Entries<AuditableEntity>()
                .Where(p => p.State == EntityState.Added || p.State == EntityState.Deleted || p.State == EntityState.Modified)
                .Where(canBeAudited);
        }
#pragma warning disable CA1031
        catch (Exception)
#pragma warning restore CA1031
        {
            auditableEntries = [];
        }

        return auditableEntries.ToLookup(e => e.State);
    }

    private static IEnumerable<DataAuditTransactionDetail> GetEntityAuditDetails(EntityEntry dbEntry, EntityState state)
    {
        string entityName = dbEntry.Entity.GetType().Name;
        string primaryKeyName = dbEntry.Metadata.FindPrimaryKey()?.Properties[0].Name;

        foreach (var propertyName in dbEntry.OriginalValues.Properties.Select(property => property.Name))
        {
            bool propertyCanBeAudited = Attribute.GetCustomAttribute(dbEntry.Entity.GetType().GetProperty(propertyName)!, typeof(NonAuditableAttribute)) == null;

            // Item1: old, Item2: new
            (object oldValue, object newValue) = GetOldAndNewValues(dbEntry, propertyName, state);

            // If new or delete, go straight to the audit. If modified, only if the property has a different value
            if (state != EntityState.Modified || !Equals(oldValue, newValue))
            {
                yield return new DataAuditTransactionDetail
                {
                    DataAuditTransactionType = state.ToAuditType(),
                    EntityName = entityName,
                    PrimaryKeyValue = dbEntry.CurrentValues[primaryKeyName]?.ToString(),
                    PropertyName = propertyName,
                    OriginalValue = propertyCanBeAudited ? GetValueString(oldValue) : "XXX",
                    NewValue = propertyCanBeAudited ? GetValueString(newValue) : "XXX",
                };
            }
        }
    }

    private static Tuple<object, object> GetOldAndNewValues(EntityEntry dbEntry, string propertyName, EntityState state)
    {
        return state switch
        {
            EntityState.Added => new Tuple<object, object>(null, dbEntry.CurrentValues[propertyName]),
            EntityState.Modified => new Tuple<object, object>(dbEntry.OriginalValues[propertyName],
                dbEntry.CurrentValues[propertyName]),
            EntityState.Deleted => new Tuple<object, object>(dbEntry.OriginalValues[propertyName], null),
            _ => throw new ArgumentException("Unrecognized entity state: " + state)
        };
    }

    private static string GetValueString(object item)
    {
        if (item == null)
        {
            return null;
        }


        if (item is not PropertyValues dbItem)
        {
            return item.ToString();
        }

        // If the property is a complex type, hydrate the object then ToString it (as DbPropertyValues does not ToString nicely)
        return dbItem.ToObject().ToString();
    }
}
