using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

public class StreamMetadataFilterConfiguration : IEntityTypeConfiguration<StreamMetadataFilter>
{
    public void Configure(EntityTypeBuilder<StreamMetadataFilter> builder)
    {
        builder.HasKey(s => s.Id);

        builder
            .Property(s => s.Id)
            .HasConversion(id => id.Value,
                value => new StreamMetadataFilterId(value))
            .ValueGeneratedNever();

        builder.Property(s => s.FilterName).HasMaxLength(100);
        builder.Property(s => s.IncludeFlag).HasColumnType("tinyint");
        builder.Property(s => s.FilterValue).HasMaxLength(255);

        builder.HasOne(s => s.Stream).WithMany(s => s.StreamMetadataFilters)
            .HasForeignKey(s => s.StreamId);
    }
}
