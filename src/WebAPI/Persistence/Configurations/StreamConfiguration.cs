using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

public class StreamConfiguration : IEntityTypeConfiguration<Stream>
{
    public void Configure(EntityTypeBuilder<Stream> builder)
    {
        builder.HasKey(s => s.Id);

        builder
            .Property(s => s.Id)
            .HasConversion(id => id.Value,
                value => new StreamId(value))
            .ValueGeneratedNever();

        builder.Property(s => s.Name).HasMaxLength(255);
        builder.Property(s => s.Prompt).HasColumnType("text");
        builder.Property(s => s.CompanyId).HasColumnType("char(36)");
        builder.Property(s => s.CompanyName).HasMaxLength(255);
        builder.Property(s => s.CompanySpecificStream).HasColumnType("bit(1)");
        builder.Property(s => s.NoOfArticles).HasColumnType("int");
        builder.Property(s => s.AiGeneratedPrompt).HasColumnType("longtext");
        builder.Property(s => s.AiGeneratedPromptId).HasColumnType("int");
        builder.Property(s => s.AiModelConfidence).HasColumnType("double");
        builder.Property(s => s.IsMention).HasColumnType("bit(1)");

        builder.HasOne(s => s.AiModel).WithMany(m => m.Streams).HasForeignKey(s => s.AiModelId);

    }
}
