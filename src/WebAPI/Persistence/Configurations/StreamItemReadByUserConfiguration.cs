using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

public class StreamItemReadByUserConfiguration : IEntityTypeConfiguration<StreamItemReadByUser>
{
    public void Configure(EntityTypeBuilder<StreamItemReadByUser> builder)
    {
        builder.HasKey(s => new { s.UserId, s.StreamItemId });

        builder.Property(s => s.ReadDateUtc).IsRequired();
    }
}
