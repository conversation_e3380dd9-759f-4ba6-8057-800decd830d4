using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

public class StreamFilterTypeCodeConfiguration : IEntityTypeConfiguration<StreamFilterTypeCode>
{
    public void Configure(EntityTypeBuilder<StreamFilterTypeCode> builder)
    {
        builder.HasKey(u => new { u.CodeId, u.StreamId });

        builder
            .Property(s => s.Id)
            .HasConversion(id => id.Value,
                value => new StreamFilterTypeCodeId(value))
            .ValueGeneratedNever();

        builder.HasOne(s => s.Stream).WithMany(s => s.StreamFilterTypeCodes)
            .HasForeignKey(s => s.StreamId);

        builder.HasOne(s => s.TypeCode).WithMany(t => t.StreamFilterTypeCodes)
            .HasForeignKey(s => s.CodeId);
    }
}
