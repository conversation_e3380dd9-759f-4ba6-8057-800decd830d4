using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

public class FewShotConfiguration : IEntityTypeConfiguration<FewShot>
{
    public void Configure(EntityTypeBuilder<FewShot> builder)
    {
        builder.HasKey(u => u.Id);

        builder
            .Property(f => f.Id)
            .HasConversion(id => id.Value,
                value => new FewShotId(value))
            .ValueGeneratedNever();

        builder.Property(f => f.ItemId).HasColumnType("BigInt");
        builder.Property(f => f.Weight).HasColumnType("double");

        builder.HasOne(f => f.Stream).WithMany(s => s.FewShots)
            .HasForeignKey(s => s.StreamId);
    }
}
