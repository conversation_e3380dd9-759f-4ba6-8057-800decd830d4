using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

internal sealed class StreamAlertConfiguration : IEntityTypeConfiguration<StreamAlert>
{
    public void Configure(EntityTypeBuilder<StreamAlert> builder)
    {
        builder.HasKey(u => u.Id);

        builder
            .Property(s => s.Id)
            .HasConversion(id => id.Value,
                value => new StreamAlertId(value))
            .ValueGeneratedNever();

        builder.Property(s => s.Frequency)
            .HasConversion(frequency => frequency.ToString(),
                value => Enum.Parse<StreamAlertFrequency>(value));

        builder.Property(s => s.Recurrence)
            .HasConversion(recurrence => recurrence.ToString(),
                value => Enum.Parse<StreamAlertRecurrence>(value));

        builder.HasOne(s => s.Stream)
            .WithMany(s => s.StreamAlerts)
            .HasForeignKey(s => s.StreamId);
    }
}
