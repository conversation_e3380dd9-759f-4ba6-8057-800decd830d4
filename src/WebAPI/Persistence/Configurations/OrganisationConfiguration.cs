using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

internal sealed class OrganisationConfiguration : IEntityTypeConfiguration<Organisation>
{
    public void Configure(EntityTypeBuilder<Organisation> builder)
    {
        builder.HasKey(u => u.Id);

        builder
            .Property(o => o.Id)
            .HasConversion(id => id.Value,
                value => new OrganisationId(value))
            .ValueGeneratedNever();

        builder.Property(p => p.Name).HasMaxLength(255);
    }
}
