using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WebAPI.Persistence.Configurations;

internal sealed class TypeCodeConfiguration : IEntityTypeConfiguration<TypeCode>
{
    public void Configure(EntityTypeBuilder<TypeCode> builder)
    {
        builder.<PERSON><PERSON>ey(u => u.Id);

        builder.Property(u => u.Id).ValueGeneratedOnAdd();

        builder.Property(p => p.ParentId).HasColumnType("int");

        builder.Property(p => p.Name);
    }
}
