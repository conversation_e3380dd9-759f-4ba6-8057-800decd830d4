using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

internal sealed class PeopleConfiguration : IEntityTypeConfiguration<People>
{
    public void Configure(EntityTypeBuilder<People> builder)
    {
        builder.HasKey(u => u.Id);

        builder
            .Property(p => p.Id)
            .HasConversion(id => id.Value,
                value => new PeopleId(value))
            .ValueGeneratedNever();

        builder.Property(p => p.FirstName).HasMaxLength(255);
        builder.Property(p => p.Surname).HasMaxLength(255);
    }
}
