using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

public class AmplifierConfiguration : IEntityTypeConfiguration<Amplifier>
{
    public void Configure(EntityTypeBuilder<Amplifier> builder)
    {
        builder.HasKey(u => u.Id);

        builder
            .Property(a => a.Id)
            .HasConversion(id => id.Value,
                value => new AmplifierId(value))
            .ValueGeneratedNever();

        builder.Property(a => a.Name).HasMaxLength(255);
        builder.Property(a => a.Weight).HasColumnType("double");

        builder.HasOne(a => a.Stream).WithMany(s => s.Amplifiers)
            .HasForeignKey(s => s.StreamId);
    }
}
