using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

internal sealed class AiModelConfiguration : IEntityTypeConfiguration<AiModel>
{
    public void Configure(EntityTypeBuilder<AiModel> builder)
    {
        builder.HasKey(u => u.Id);

        builder
            .Property(o => o.Id)
            .HasConversion(id => id.Value,
                value => new AiModelId(value))
            .ValueGeneratedNever();

        builder.Property(p => p.Name).HasMaxLength(255);
        builder.Property(p => p.Confidence).HasColumnType("double");
    }
}
