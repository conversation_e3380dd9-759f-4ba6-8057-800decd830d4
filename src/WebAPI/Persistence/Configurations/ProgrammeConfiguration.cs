using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SharedKernel.Entities;

namespace WebAPI.Persistence.Configurations;

internal sealed class ProgrammeConfiguration : IEntityTypeConfiguration<Programme>
{
    public void Configure(EntityTypeBuilder<Programme> builder)
    {
        builder.HasKey(u => u.Id);

        builder
            .Property(p => p.Id)
            .HasConversion(id => id.Value,
                value => new ProgrammeId(value))
            .ValueGeneratedNever();

        builder.Property(p => p.Name);
    }
}
