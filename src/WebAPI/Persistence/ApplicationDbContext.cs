using System.Linq.Expressions;
using SharedKernel.Entities;
using SharedKernel.Primitives.Abstract;
using SharedKernel.Services;

namespace WebAPI.Persistence;

public sealed class ApplicationDbContext : AuditableContext
{
    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        IDateTimeService dateTimeService,
        ICurrentUserService currentUserService) : base(options, dateTimeService, currentUserService)
    { }

    public DbSet<AiModel> AiModels => Set<AiModel>();
    public DbSet<Amplifier> Amplifier => Set<Amplifier>();
    public DbSet<FewShot> FewShot => Set<FewShot>();
    public DbSet<Organisation> Organisations => Set<Organisation>();
    public DbSet<People> Peoples => Set<People>();
    public DbSet<Programme> Programmes => Set<Programme>();
    public DbSet<StreamAlert> StreamAlerts => Set<StreamAlert>();
    public DbSet<StreamFilterTypeCode> StreamFilterTypeCode => Set<StreamFilterTypeCode>();
    public DbSet<StreamMetadataFilter> StreamMetadataFilter => Set<StreamMetadataFilter>();
    public DbSet<Stream> Streams => Set<Stream>();
    public DbSet<TypeCode> TypeCodes => Set<TypeCode>();
    public DbSet<StreamItemReadByUser> StreamItemReadByUser => Set<StreamItemReadByUser>();
    public DbSet<StreamAlertLog> StreamAlertLogs => Set<StreamAlertLog>();

    public DbSet<PeopleTag> PeopleTags => Set<PeopleTag>();
    public DbSet<OrganisationTag> OrganisationTags => Set<OrganisationTag>();
    public DbSet<ProgrammeTag> ProgrammeTags => Set<ProgrammeTag>();
    public DbSet<ThemeTag> ThemeTags => Set<ThemeTag>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        // Global turn off delete behaviour on foreign keys
        foreach (var relationship in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))
        {
            relationship.DeleteBehavior = DeleteBehavior.Restrict;
        }

        ConfigureSoftDelete(modelBuilder);

        base.OnModelCreating(modelBuilder);
    }

    private static void ConfigureSoftDelete(ModelBuilder modelBuilder)
    {
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
            {
                modelBuilder.Entity(entityType.ClrType)
                    .HasQueryFilter(ConvertFilterExpression(entityType.ClrType));
            }
        }

        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
            {
                modelBuilder.Entity(entityType.ClrType)
                    .HasIndex("IsDeleted")
                    .HasFilter("IsDeleted = 0");
            }
        }
    }

    /// <summary>
    /// Helper method to create a filter expression for `IsDeleted`
    /// </summary>
    /// <param name="entityType"></param>
    /// <returns></returns>
    private static LambdaExpression ConvertFilterExpression(Type entityType)
    {
        var parameter = Expression.Parameter(entityType, "e");
        var property = Expression.Property(parameter, "IsDeleted");
        var constant = Expression.Constant(false);
        var body = Expression.Equal(property, constant);

        return Expression.Lambda(body, parameter);
    }
}
