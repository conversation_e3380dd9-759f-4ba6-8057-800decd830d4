{"AllowedHosts": "*", "Betterstack": {"Token": "..."}, "AirMonitoringApi": {"Url": "https://colibri/", "RequestAttemptTimeoutInSeconds": 60, "RequestTotalTimeoutInSeconds": 120, "MetadataUpdateFrequencyInMinutes": 60}, "UserApi": {"Url": "https://user/"}, "NotificationApi": {"Url": "https://notification/"}, "HangfireApi": {"Url": "https://hangfire/"}, "MySQL": {"UserName": "user", "Password": "password", "Database": "StreamsDb", "HostName": "host", "Port": 3306}, "JwtConfigurations": {"Audience": "https://dehavilland.co.uk/", "Issuer": "https://dehavilland.co.uk/", "Key": "..."}, "OpenSearch": {"UserName": "user", "Password": "password", "HostName": "host", "Protocol": "https", "Port": 9200, "DefaultIndex": "content_apollo"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "Filter": [{"Name": "ByExcluding", "Args": {"expression": "RequestPath like '/health%'"}}]}, "Caching": {"UseDistributedCache": false, "DistributedCacheConfigurationString": "redis.url:6379"}}